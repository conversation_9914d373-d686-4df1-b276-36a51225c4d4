package com.cleevio.fortraders.application.module.prizedistribution

import com.cleevio.fortraders.application.common.BaseFinderService
import com.cleevio.fortraders.domain.model.prizedistribution.PrizeDistribution
import com.cleevio.fortraders.domain.model.prizedistribution.PrizeDistributionRepository
import com.cleevio.fortraders.domain.model.prizedistribution.exception.PrizeDistributionNotFoundException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class PrizeDistributionFinderService(
    prizeDistributionRepository: PrizeDistributionRepository,
) : BaseFinderService<PrizeDistribution, PrizeDistributionRepository>(
    repository = prizeDistributionRepository,
    notFoundException = ::PrizeDistributionNotFoundException
) {
    @Transactional(readOnly = true)
    fun existsByPlayersAndPosition(players: Int, position: Int): Boolean =
        repository.existsByPlayersAndPosition(players = players, position = position)
}
