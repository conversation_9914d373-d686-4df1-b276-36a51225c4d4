package com.cleevio.fortraders.application.module.tradingaccount.event.listener

import com.cleevio.fortraders.application.module.tradingaccount.TradingAccountFeedService
import com.cleevio.fortraders.application.module.tradingaccount.TradingAccountService
import com.cleevio.fortraders.application.module.tradingaccountpause.TradingAccountPauseMonitoringService
import com.cleevio.fortraders.domain.model.breach.event.BreachCreatedEvent
import com.cleevio.fortraders.domain.model.breach.event.BreachDeletedEvent
import com.cleevio.fortraders.domain.model.breach.event.BreachVerifiedForTradingAccountsEvent
import org.springframework.context.event.EventListener
import org.springframework.core.Ordered
import org.springframework.core.annotation.Order
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class BreachTradingAccountEventListener(
    private val tradingAccountService: TradingAccountService,
    private val tradingAccountFeedService: TradingAccountFeedService,
    private val tradingAccountPauseMonitoringService: TradingAccountPauseMonitoringService,
) {
    @EventListener
    @Order(Ordered.HIGHEST_PRECEDENCE)
    fun handleBreachCreatedEvent(event: BreachCreatedEvent) {
        tradingAccountService.breachAccount(
            id = event.tradingAccountId
        )
    }

    @EventListener
    @Order(Ordered.LOWEST_PRECEDENCE)
    fun handleBreachDeletedEvent(event: BreachDeletedEvent) {
        tradingAccountService.reactivateBreachedAccount(event.tradingAccountId)
    }

    @EventListener
    fun handleBreachVerifiedForTradingAccountsEvent(event: BreachVerifiedForTradingAccountsEvent) {
        tradingAccountPauseMonitoringService.pauseAndUnpauseTradingAccounts()
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    fun handleAsyncBreachCreatedEvent(event: BreachCreatedEvent) {
        tradingAccountService.upgradeTradingAccountAfterBreach(
            id = event.tradingAccountId,
            breachType = event.type
        )
        tradingAccountFeedService.cancelFeedSubscriptionsAfterBreach(
            tradingAccountId = event.tradingAccountId,
            breachType = event.type
        )
    }
}
