package com.cleevio.fortraders.application.module.order.command

import com.cleevio.fortraders.application.common.command.Command
import com.cleevio.fortraders.domain.model.transaction.constant.PaymentGatewayProvider
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionPaymentMethod
import io.swagger.v3.oas.annotations.media.Schema
import java.util.UUID

data class PayOrderCommand(
    val userId: UUID,
    val orderId: UUID,
    val paymentMethod: TransactionPaymentMethod,
    val clickId: String?
) : Command<PayOrderCommand.Result> {

    @Schema(name = "PayOrderResult")
    data class Result(
        val depositTransactionId: UUID,
        val paymentGatewayProvider: PaymentGatewayProvider,
        val providerPaymentId: String,
        val providerPaymentSecret: String?,
    )
}
