package com.cleevio.fortraders.application.module.transaction.command

import com.cleevio.fortraders.application.common.command.Command
import io.swagger.v3.oas.annotations.media.Schema
import java.util.UUID

data class ExecuteTransactionCommand(
    val transactionId: UUID,
) : Command<ExecuteTransactionCommand.Result> {

    @Schema(name = "ExecuteTransactionResult")
    data class Result(
        val transactionId: UUID,
        val paymentUrl: String?,
    )
}
