package com.cleevio.fortraders.application.module.affiliate

import com.cleevio.fortraders.application.common.command.CommandHandler
import com.cleevio.fortraders.application.common.constant.AFFILIATE
import com.cleevio.fortraders.application.common.util.ifTrue
import com.cleevio.fortraders.application.module.affiliate.command.AdminPatchAffiliateUserCommand
import com.cleevio.fortraders.application.module.affiliate.constant.CREATE_OR_UPDATE_AFFILIATE
import com.cleevio.fortraders.domain.model.affiliate.exception.AffiliateWithGivenCouponCodeAlreadyExistsException
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import com.cleevio.library.lockinghandler.service.LockService
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class AdminPatchAffiliateUserCommandHandler(
    private val lockService: LockService,
    private val affiliateFinderService: AffiliateFinderService,
) : Command<PERSON>andler<Unit, AdminPatchAffiliateUserCommand> {
    override val command = AdminPatchAffiliateUserCommand::class

    @Transactional
    @Lock(module = AFFILIATE, lockName = CREATE_OR_UPDATE_AFFILIATE)
    override fun invoke(@LockFieldParameter("userId") command: AdminPatchAffiliateUserCommand) {
        lockService.obtainBlockingLock(
            module = AFFILIATE,
            lockName = CREATE_OR_UPDATE_AFFILIATE,
            command.couponCode.toString()
        ).use {
            val affiliate = affiliateFinderService.getByUserId(command.userId)

            if (command.couponCode != null && command.couponCode.uppercase() != affiliate.couponCode) {
                affiliateFinderService.existsByCouponCode(command.couponCode).ifTrue {
                    throw AffiliateWithGivenCouponCodeAlreadyExistsException()
                }
            }

            affiliate.patchAdminSpecifiedProperties(
                state = command.state,
                couponCode = command.couponCode,
                commissionPercentage = command.commissionPercentage,
                discountAmountPercentage = command.discountAmountPercentage,
                note = command.note
            )
        }
    }
}
