package com.cleevio.fortraders.application.module.file.command

import com.cleevio.fortraders.application.common.command.Command
import com.cleevio.fortraders.application.common.command.IdResult
import com.cleevio.fortraders.application.common.validation.ValidImage
import com.cleevio.fortraders.domain.model.file.constant.FileType
import org.springframework.web.multipart.MultipartFile

data class AdminImageUploadCommand(
    @field:ValidImage val file: MultipartFile,
    val fileType: FileType,
) : Command<IdResult>
