package com.cleevio.fortraders.application.module.invoice.event.listener

import com.cleevio.fortraders.application.module.invoice.InvoiceProcessingService
import com.cleevio.fortraders.domain.model.transaction.event.TransactionCreatedEvent
import com.cleevio.fortraders.domain.model.transaction.event.TransactionStatusChangedEvent
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class TransactionInvoiceEventListener(
    private val invoiceProcessingService: InvoiceProcessingService,
) {
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    fun handleAsyncTransactionStatusChangedEvent(event: TransactionStatusChangedEvent) {
        invoiceProcessingService.createInvoiceAfterSuccessfulOrderPayment(
            transactionId = event.transactionId,
            transactionType = event.type,
            transactionStatus = event.newStatus
        )
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    fun handleAsyncTransactionCreatedEvent(event: TransactionCreatedEvent) {
        invoiceProcessingService.createInvoiceAfterSuccessfulOrderPayment(
            transactionId = event.transactionId,
            transactionType = event.type,
            transactionStatus = event.newStatus
        )
    }
}
