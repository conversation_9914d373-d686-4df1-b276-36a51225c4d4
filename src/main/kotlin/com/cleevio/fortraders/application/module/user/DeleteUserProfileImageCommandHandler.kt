package com.cleevio.fortraders.application.module.user

import com.cleevio.fortraders.application.common.command.CommandHandler
import com.cleevio.fortraders.application.common.constant.USER
import com.cleevio.fortraders.application.module.file.FileService
import com.cleevio.fortraders.application.module.user.command.DeleteUserProfileImageCommand
import com.cleevio.fortraders.application.module.user.constant.UPDATE_USER
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class DeleteUserProfileImageCommandHandler(
    private val fileService: FileService,
    private val userFinderService: UserFinderService,
) : CommandHandler<Unit, DeleteUserProfileImageCommand> {
    override val command = DeleteUserProfileImageCommand::class

    @Transactional
    @Lock(module = USER, lockName = UPDATE_USER)
    override fun invoke(@LockFieldParameter("userId") command: DeleteUserProfileImageCommand) {
        val user = userFinderService.getById(command.userId)
        user.profileImageFileId?.let { fileService.delete(it) }
        user.updateProfileImage(null)
    }
}
