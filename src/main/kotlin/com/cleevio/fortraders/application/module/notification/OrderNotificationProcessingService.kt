package com.cleevio.fortraders.application.module.notification

import com.cleevio.fortraders.application.common.util.toEndOfTheDay
import com.cleevio.fortraders.application.common.util.toStartOfTheDay
import com.cleevio.fortraders.application.module.affiliate.AffiliateFinderService
import com.cleevio.fortraders.application.module.challenge.ChallengeFinderService
import com.cleevio.fortraders.application.module.challengeplan.ChallengePlanFinderService
import com.cleevio.fortraders.application.module.challengestep.ChallengeStepFinderService
import com.cleevio.fortraders.application.module.contact.ContactFinderService
import com.cleevio.fortraders.application.module.country.CountryFinderService
import com.cleevio.fortraders.application.module.discountcode.DiscountCodeFinderService
import com.cleevio.fortraders.application.module.notification.port.output.AdminSendOrderPaidNotification
import com.cleevio.fortraders.application.module.notification.port.output.toEmailVariables
import com.cleevio.fortraders.application.module.order.OrderFinderService
import com.cleevio.fortraders.application.module.systemsetting.SystemSettingFinderService
import com.cleevio.fortraders.application.module.tradingaccount.TradingAccountFinderService
import com.cleevio.fortraders.application.module.tradingaccount.config.TradingAccountPlatformsConfiguration
import com.cleevio.fortraders.application.module.transaction.TransactionFinderService
import com.cleevio.fortraders.application.module.user.UserFinderService
import com.cleevio.fortraders.domain.model.mailtemplate.constant.MailType
import com.cleevio.fortraders.infrastructure.config.logger
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.time.Instant
import java.time.ZoneId
import java.util.UUID

@Service
class OrderNotificationProcessingService(
    private val orderFinderService: OrderFinderService,
    private val userFinderService: UserFinderService,
    private val contactFinderService: ContactFinderService,
    private val countryFinderService: CountryFinderService,
    private val challengeFinderService: ChallengeFinderService,
    private val challengePlanFinderService: ChallengePlanFinderService,
    private val challengeStepFinderService: ChallengeStepFinderService,
    private val tradingAccountFinderService: TradingAccountFinderService,
    private val transactionFinderService: TransactionFinderService,
    private val discountCodeFinderService: DiscountCodeFinderService,
    private val affiliateFinderService: AffiliateFinderService,
    private val adminSendOrderPaidNotification: AdminSendOrderPaidNotification,
    private val mailNotificationService: MailNotificationService,
    private val tradingAccountPlatformsConfiguration: TradingAccountPlatformsConfiguration,

    @Value("\${integration.slack.notification-timezone}")
    private val slackNotificationTimezone: String,
    private val systemSettingFinderService: SystemSettingFinderService,
    private val abandonedOrderService: AbandonedOrderService,
) {
    private val logger = logger()

    fun processOrderPaymentCompleted(orderId: UUID) {
        orderFinderService.getById(orderId).also {
            if (!it.isCompleted()) {
                logger.error("Order with id $orderId is not completed yet")
                return
            }
        }
        sendOrderPaidAdminNotification(orderId, isRecurringPayment = false)
    }

    fun processOrderPendingCancellation(orderId: UUID) {
        sendOrderPaymentNotification(
            orderId = orderId,
            type = MailType.SUBSCRIPTION_CANCELLED
        )
    }

    fun processOrderRecurringPaymentCompleted(orderId: UUID) {
        sendOrderPaidAdminNotification(orderId, isRecurringPayment = true)
        sendOrderPaymentNotification(
            orderId = orderId,
            type = MailType.SUBSCRIPTION_PAYMENT_SUCCESS
        )
    }

    fun processOrderRecurringPaymentFailed(orderId: UUID) {
        sendOrderPaymentNotification(
            orderId = orderId,
            type = MailType.SUBSCRIPTION_PAYMENT_FAILED
        )
    }

    fun processAbandonedOrders() {
        val inactivityPeriodMinutes = systemSettingFinderService.getUncompletedOrderProcessNotificationTimePeriodMinutes()
        val abandonedOrderIds = orderFinderService.findAllPendingWithTimePeriodInactivityAndNotNotified(
            inactivityPeriodMinutes = inactivityPeriodMinutes,
            mailType = MailType.ORDER_PROCESS_ABANDONED
        )

        if (abandonedOrderIds.isEmpty()) return

        abandonedOrderIds.forEach {
            abandonedOrderService.processNotification(it)
        }
    }

    private fun sendOrderPaidAdminNotification(orderId: UUID, isRecurringPayment: Boolean = false) {
        val order = orderFinderService.getById(orderId)
        val creditsUsed = transactionFinderService.getSumOfCreditsUsedInOrder(orderId)
        val user = userFinderService.getById(order.userId)
        val contact = contactFinderService.getByUserId(order.userId)
        val country = countryFinderService.getById(contact.countryId)
        val challenge = challengeFinderService.getById(order.challengeId)
        val challengePlan = challengePlanFinderService.getById(order.challengePlanId)
        val discountCode = order.discountCodeId?.let { discountCodeFinderService.getById(it) }
        val affiliate = order.affiliateId?.let { affiliateFinderService.getById(it) }
        val firstPaymentTransaction = transactionFinderService.findFirstCompletedOrderDepositPayment(order.id)

        val zoneOffset = ZoneId.of(slackNotificationTimezone).rules.getOffset(Instant.now())
        val todayTimeRange = Instant.now().let {
            it.toStartOfTheDay(zoneOffset)..it.toEndOfTheDay(zoneOffset)
        }
        val ordersCompletedThisMonth = orderFinderService.findAllOrdersPaidThisMonth(zoneOffset)
        val ordersCompletedThisDay = ordersCompletedThisMonth.filter {
            val paidAt = it.paidAt
            paidAt != null && paidAt in todayTimeRange
        }
        val paidUserOrderStats = orderFinderService.findPaidOrderStatsByUserId(user.id)
        val sumOfUserChallengeOrderTransactions = transactionFinderService.getSumOfChallengeOrderTransactionsByUserId(user.id)

        adminSendOrderPaidNotification(
            orderId = order.id,
            orderName = "${challenge.name} - ${challengePlan.title} ${challengePlan.category.toHumanReadableFormat()}".trim(),
            orderPrice = order.price,
            creditsUsed = creditsUsed,
            affiliateCodeUsed = affiliate?.couponCode,
            discountCodeUsed = discountCode?.code,
            paymentProvider = firstPaymentTransaction?.provider?.toHumanReadableFormat() ?: "Wallet",
            platformType = order.platform,
            userFullName = user.getFullName(),
            email = user.email,
            city = contact.city,
            postCode = contact.postCode,
            countryName = country.name,
            countryIsoCode = country.isoCode,
            userOrdersTotal = paidUserOrderStats.count.toInt(),
            userOrdersRevenueTotal = sumOfUserChallengeOrderTransactions,
            ordersThisDay = ordersCompletedThisDay.size,
            ordersRevenueThisDay = ordersCompletedThisDay.sumOf { it.price },
            ordersThisMonth = ordersCompletedThisMonth.size,
            ordersRevenueThisMonth = ordersCompletedThisMonth.sumOf { it.price },
            isRecurringPayment = isRecurringPayment
        )
    }

    private fun sendOrderPaymentNotification(orderId: UUID, type: MailType) {
        val order = orderFinderService.getById(orderId)
        val user = userFinderService.getById(order.userId)
        val tradingAccount = tradingAccountFinderService.getLatestByOrderId(
            orderId = orderId
        )
        val challengeStep = challengeStepFinderService.getById(tradingAccount.challengeStepId)

        mailNotificationService.sendEmailNotificationToUser(
            userId = order.userId,
            subjectId = orderId,
            type = type,
            mailVariables = tradingAccount.toEmailVariables(
                challengeStepNumber = challengeStep.number,
                login = tradingAccount.getPlatformLogin(user.email),
                tradingAccountPlatformsConfiguration = tradingAccountPlatformsConfiguration
            )
        )
    }
}
