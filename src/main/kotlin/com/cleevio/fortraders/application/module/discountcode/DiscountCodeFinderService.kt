package com.cleevio.fortraders.application.module.discountcode

import com.cleevio.fortraders.application.common.BaseFinderService
import com.cleevio.fortraders.domain.model.discountcode.DiscountCode
import com.cleevio.fortraders.domain.model.discountcode.DiscountCodeRepository
import com.cleevio.fortraders.domain.model.discountcode.constant.DiscountCodeState
import com.cleevio.fortraders.domain.model.discountcode.exception.DiscountCodeNotFoundException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class DiscountCodeFinderService(
    discountCodeRepository: DiscountCodeRepository,
) : BaseFinderService<DiscountCode, DiscountCodeRepository>(
    repository = discountCodeRepository,
    notFoundException = ::DiscountCodeNotFoundException
) {
    @Transactional(readOnly = true)
    fun existsByCode(code: String): Boolean = repository.existsByCode(code.uppercase())

    @Transactional(readOnly = true)
    fun getEnabledByCode(code: String): DiscountCode = repository.findByCodeAndState(
        code = code.uppercase(),
        state = DiscountCodeState.ENABLED
    ) ?: throw notFoundException()
}
