package com.cleevio.fortraders.application.module.futureorder.port.output.dto

import com.cleevio.fortraders.domain.model.futureorder.constant.FutureTradeSide
import com.cleevio.fortraders.domain.model.futureorder.constant.FutureTradeType
import java.math.BigDecimal
import java.time.Instant

data class UserFutureOrderModel(
    val orderId: String,
    val symbol: String,
    val type: FutureTradeType,
    val quantity: Int,
    val averagePrice: BigDecimal,
    val profit: BigDecimal?,
    val side: FutureTradeSide,
    val commission: BigDecimal,
    val transactionTime: Instant,
)
