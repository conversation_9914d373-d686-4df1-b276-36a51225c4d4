package com.cleevio.fortraders.application.module.instrument.scheduled

import com.cleevio.fortraders.application.common.constant.INSTRUMENT
import com.cleevio.fortraders.application.module.instrument.InstrumentQuotePricesPublishingService
import com.cleevio.fortraders.application.module.instrument.constant.PUBLISH_INSTRUMENT_QUOTE_PRICES
import com.cleevio.library.lockinghandler.service.TryLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class InstrumentPricesPublishingTrigger(
    private val instrumentQuotePricesPublishingService: InstrumentQuotePricesPublishingService,
) {
    @TryLock(module = INSTRUMENT, lockName = PUBLISH_INSTRUMENT_QUOTE_PRICES)
    @Scheduled(cron = "\${fortraders.instrument.prices-publishing.cron}")
    fun trigger() {
        instrumentQuotePricesPublishingService.publishInstrumentQuotePrices()
    }
}
