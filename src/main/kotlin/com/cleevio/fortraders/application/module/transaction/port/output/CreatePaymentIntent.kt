package com.cleevio.fortraders.application.module.transaction.port.output

import com.cleevio.fortraders.domain.model.transaction.constant.Currency
import com.cleevio.fortraders.domain.model.transaction.constant.PaymentGatewayProvider
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionPaymentMethod
import java.math.BigDecimal
import java.util.UUID

interface CreatePaymentIntent {
    val paymentMethod: TransactionPaymentMethod

    operator fun invoke(
        transactionId: UUID,
        amount: BigDecimal,
        currency: Currency,
        city: String,
        countryIsoCode: String,
        postCode: String,
        street: String,
        email: String,
        firstName: String?,
        lastName: String?,
        isSubscription: Boolean,
        fullPhoneNumber: String?,
    ): Result<CreatePaymentIntentResult>
}

fun List<CreatePaymentIntent>.getForPaymentMethod(paymentMethod: TransactionPaymentMethod): CreatePaymentIntent =
    first { it.paymentMethod == paymentMethod }

data class CreatePaymentIntentResult(
    val paymentId: String,
    val paymentSecret: String?,
    val paymentGatewayProvider: PaymentGatewayProvider,
)
