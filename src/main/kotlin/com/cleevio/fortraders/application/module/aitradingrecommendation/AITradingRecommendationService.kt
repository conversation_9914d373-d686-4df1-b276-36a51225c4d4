package com.cleevio.fortraders.application.module.aitradingrecommendation

import com.cleevio.fortraders.application.module.aitradingrecommendation.port.output.AITradingRecommendationResult
import com.cleevio.fortraders.domain.model.aitradingrecommendation.AITradingRecommendationCreateService
import com.cleevio.fortraders.domain.model.aitradingrecommendationdetail.AITradingRecommendationDetailCreateService
import com.cleevio.fortraders.domain.model.aitradingrecommendationdetail.constant.AITradingRecommendationDetailType
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class AITradingRecommendationService(
    private val aiTradingRecommendationCreateService: AITradingRecommendationCreateService,
    private val aiTradingRecommendationDetailCreateService: AITradingRecommendationDetailCreateService,
) {
    @Transactional
    fun createAITradingRecommendation(tradingAccountId: UUID, aiTradingRecommendationResult: AITradingRecommendationResult) {
        val aiTradingRecommendation = aiTradingRecommendationCreateService.create(
            tradingAccountId = tradingAccountId
        )

        aiTradingRecommendationResult.strengths.forEach {
            aiTradingRecommendationDetailCreateService.create(
                aiTradingRecommendationId = aiTradingRecommendation.id,
                title = it.title,
                text = it.text,
                type = AITradingRecommendationDetailType.STRENGTHS
            )
        }
        aiTradingRecommendationResult.actionableRecommendations.forEach {
            aiTradingRecommendationDetailCreateService.create(
                aiTradingRecommendationId = aiTradingRecommendation.id,
                title = it.title,
                text = it.text,
                type = AITradingRecommendationDetailType.ACTIONABLE_RECOMMENDATIONS
            )
        }
        aiTradingRecommendationResult.areasForImprovement.forEach {
            aiTradingRecommendationDetailCreateService.create(
                aiTradingRecommendationId = aiTradingRecommendation.id,
                title = it.title,
                text = it.text,
                type = AITradingRecommendationDetailType.AREAS_FOR_IMPROVEMENT
            )
        }
    }
}
