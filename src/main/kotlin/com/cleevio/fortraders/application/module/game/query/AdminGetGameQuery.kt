package com.cleevio.fortraders.application.module.game.query

import com.cleevio.fortraders.application.common.query.Query
import com.cleevio.fortraders.domain.model.game.constant.GameState
import com.cleevio.fortraders.domain.model.game.constant.GameType
import com.cleevio.fortraders.domain.model.instrument.constant.InstrumentType
import com.cleevio.fortraders.domain.model.tournament.constant.TournamentPrizePoolType
import com.cleevio.fortraders.domain.model.tournament.constant.TournamentReward
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.time.Instant
import java.util.UUID

data class AdminGetGameQuery(
    val gameId: UUID,
) : Query<AdminGetGameQuery.Result> {

    @Schema(name = "AdminGetGameResult")
    data class Result(
        val id: UUID,
        val type: GameType,
        val state: GameState,
        val name: String,
        val description: String,
        val logoFileId: UUID,
        val logoUrl: String,
        val createdAt: Instant,
        val updatedAt: Instant,
        val instruments: List<InstrumentDetails>,
        val tournaments: List<TournamentDetails>,
    )

    @Schema(name = "AdminGetGameResultInstrumentDetails")
    data class InstrumentDetails(
        val id: UUID,
        val type: InstrumentType,
        val name: String,
        val ticker: String,
        val spreadPercentage: BigDecimal,
        val createdAt: Instant,
        val updatedAt: Instant,
    )

    @Schema(name = "AdminGetGameResultTournamentDetails")
    data class TournamentDetails(
        val id: UUID,
        val name: String,
        val reward: TournamentReward,
        val initialPrizePool: BigDecimal?,
        val prizePoolType: TournamentPrizePoolType,
        val entryFee: BigDecimal,
        val buyInsLimit: Int?,
        val maxDrawdown: Int?,
        val rules: String?,
        val startsAt: Instant,
        val endsAt: Instant,
        val leverageEnabled: Boolean,
        val coverUrl: String,
        val createdAt: Instant,
        val updatedAt: Instant,
    )
}
