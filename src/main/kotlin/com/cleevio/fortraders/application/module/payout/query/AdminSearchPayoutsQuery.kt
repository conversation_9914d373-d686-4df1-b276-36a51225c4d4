package com.cleevio.fortraders.application.module.payout.query

import com.cleevio.fortraders.application.common.query.Query
import com.cleevio.fortraders.domain.model.breach.constant.BreachType
import com.cleevio.fortraders.domain.model.challenge.constant.ChallengeType
import com.cleevio.fortraders.domain.model.payout.constant.PayoutFinalState
import com.cleevio.fortraders.domain.model.payout.constant.PayoutKycState
import com.cleevio.fortraders.domain.model.payout.constant.PayoutRiskState
import com.cleevio.fortraders.domain.model.payout.constant.PayoutState
import com.cleevio.fortraders.domain.model.tradingaccount.constant.TradingAccountState
import com.cleevio.fortraders.domain.model.user.constant.UserKycState
import com.cleevio.fortraders.domain.model.verificationcall.constant.VerificationCallState
import com.cleevio.fortraders.domain.model.verificationcall.constant.VerificationCallType
import io.swagger.v3.oas.annotations.media.Schema
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import java.math.BigDecimal
import java.time.Instant
import java.util.UUID

data class AdminSearchPayoutsQuery(
    val pageable: Pageable,
    val filter: Filter,
) : Query<Page<AdminSearchPayoutsQuery.ResultWithTotalPayouts>> {
    data class Filter(
        val states: Set<PayoutState>?,
        val createdAtFrom: Instant?,
        val createdAtTo: Instant?,
        val labels: Set<UUID>?,
        val fulltext: String?,
        val challengeTypes: Set<ChallengeType>?,
    )

    @Schema(name = "AdminSearchPayoutsResultWithTotalPayouts")
    data class ResultWithTotalPayouts(
        val result: Result,
        val pendingPayoutsTotal: BigDecimal,
        val pendingPayoutsCount: Int,
    )

    @Schema(name = "AdminSearchPayoutsResult")
    data class Result(
        val id: UUID,
        val riskState: PayoutRiskState,
        val kycState: PayoutKycState,
        val finalState: PayoutFinalState,
        val state: PayoutState,
        val requestedAmount: BigDecimal,
        val totalAmount: BigDecimal,
        val amountAfterSplit: BigDecimal,
        val bonusAmount: BigDecimal?,
        val note: String?,
        val internalReason: String?,
        val externalReason: String?,
        val shouldCountdown: Boolean,
        val maxMargin: BigDecimal,
        val finalisedAt: Instant?,
        val createdAt: Instant,
        val updatedAt: Instant,
        val order: OrderDetail,
        val tradingAccount: TradingAccountDetail,
        val labels: List<LabelDetail>,
        val verificationCalls: List<VerificationCallDetail>,
    )

    @Schema(name = "AdminSearchPayoutsResultOrderDetail")
    data class OrderDetail(
        val id: UUID,
        val profitSplit: Int,
        val user: UserDetail,
        val challenge: ChallengeDetail,
        val discountCode: DiscountCodeDetail?,
        val affiliateCode: AffiliateCodeDetail?,
    )

    @Schema(name = "AdminSearchPayoutsResultUserDetail")
    data class UserDetail(
        val id: UUID,
        val firstName: String?,
        val lastName: String?,
        val email: String,
        val kycState: UserKycState,
        val payoutsCount: Int,
        val payoutsTotal: BigDecimal,
        val depositTransactionsTotal: BigDecimal,
        val country: CountryDetail?,
    )

    @Schema(name = "AdminSearchPayoutsResultCountryDetail")
    data class CountryDetail(
        val name: String,
        val isoCode: String,
    )

    @Schema(name = "AdminSearchPayoutsResultChallengeDetail")
    data class ChallengeDetail(
        val id: UUID,
        val name: String,
        val type: ChallengeType,
    )

    @Schema(name = "AdminSearchPayoutsResultDiscountCodeDetail")
    data class DiscountCodeDetail(
        val id: UUID,
        val code: String,
    )

    @Schema(name = "AdminSearchPayoutsResultAffiliateCodeDetail")
    data class AffiliateCodeDetail(
        val id: UUID,
        val code: String,
    )

    @Schema(name = "AdminSearchPayoutsResultTradingAccountDetail")
    data class TradingAccountDetail(
        val id: UUID,
        val state: TradingAccountState,
        val accountId: String,
        val breach: BreachDetail?,
        val startingBalance: BigDecimal,
        val plan: String,
    )

    @Schema(name = "AdminSearchPayoutsResultBreachDetail")
    data class BreachDetail(
        val breachType: BreachType,
        val breachedAt: Instant,
        val externalReason: String?,
        val internalReason: String?,
    )

    @Schema(name = "AdminSearchPayoutsResultLabelDetail")
    data class LabelDetail(
        val id: UUID,
        val name: String,
    )

    @Schema(name = "AdminSearchPayoutsResultVerificationCallDetail")
    data class VerificationCallDetail(
        val type: VerificationCallType,
        val state: VerificationCallState,
        val startsAt: Instant?,
        val endsAt: Instant?,
        val meetingUrl: String?,
        val recordingUrl: String?,
        val note: String?,
        val supportUsers: Set<UUID>,
    )
}
