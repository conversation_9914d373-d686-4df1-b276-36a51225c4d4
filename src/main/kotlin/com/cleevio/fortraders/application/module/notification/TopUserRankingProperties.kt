package com.cleevio.fortraders.application.module.notification

import jakarta.validation.constraints.NotBlank
import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "fortraders.notification.top-user-ranking")
data class TopUserRankingProperties(
    @field:NotBlank val secretKey: String,
    @field:NotBlank val topInstrumentsCountForInstrumentRanking: Int,
    @field:NotBlank val minTradersCountForInstrumentsRanking: Int,
)
