package com.cleevio.fortraders.application.module.contract.query

import com.cleevio.fortraders.application.common.query.Query
import com.cleevio.fortraders.domain.model.contracttemplate.constant.ContractTemplateVariable
import io.swagger.v3.oas.annotations.media.Schema
import java.util.UUID

class AdminGetContractTemplatesQuery : Query<List<AdminGetContractTemplatesQuery.Result>> {

    @Schema(name = "AdminGetContractTemplatesResult")
    data class Result(
        val id: UUID,
        val name: String,
        val externalId: Long,
        val supportedVariables: Set<ContractTemplateVariable>,
    )
}
