package com.cleevio.fortraders.application.module.challenge.query

import com.cleevio.fortraders.application.common.query.Query
import com.cleevio.fortraders.domain.model.challenge.constant.ChallengeState
import com.cleevio.fortraders.domain.model.challenge.constant.ChallengeType
import com.cleevio.fortraders.domain.model.challengeplan.constant.ChallengePlanCategory
import com.cleevio.fortraders.domain.model.challengeplan.constant.ChallengePlanState
import com.cleevio.fortraders.domain.model.challengestep.constant.ChallengeStepDailyDrawdownType
import com.cleevio.fortraders.domain.model.challengestep.constant.ChallengeStepDailyPauseType
import com.cleevio.fortraders.domain.model.challengestep.constant.ChallengeStepMaxDrawdownType
import com.cleevio.fortraders.domain.model.challengestep.constant.ChallengeStepType
import com.cleevio.fortraders.domain.model.challengestepsetting.constant.ChallengeStepSettingType
import com.cleevio.fortraders.domain.model.challengestepsettingmovement.constant.ChallengeStepSettingMovementType
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.time.Instant
import java.util.UUID

data class AdminGetChallengeQuery(
    val challengeId: UUID,
) : Query<AdminGetChallengeQuery.Result> {

    @Schema(name = "AdminGetChallengeResult")
    data class Result(
        val id: UUID,
        val name: String,
        val startingBalance: BigDecimal,
        val state: ChallengeState,
        val type: ChallengeType,
        val createdAt: Instant,
        val updatedAt: Instant,
        val plans: List<AdminChallengePlanResult>,
    )

    @Schema(name = "AdminChallengePlanResult")
    data class AdminChallengePlanResult(
        val id: UUID,
        val category: ChallengePlanCategory,
        val stepCount: Int,
        val externalProductId: Int?,
        val state: ChallengePlanState,
        val title: String,
        val basePrice: BigDecimal,
        val isSubscription: Boolean,
        val platforms: List<PlatformType>,
        val capTrailingDrawdownAfterPayout: Boolean,
        val minimumPayoutLimit: BigDecimal,
        val maximumPayoutLimit: BigDecimal?,
        val createdAt: Instant,
        val updatedAt: Instant,
        val steps: List<AdminChallengeStepResult>,
    )

    @Schema(name = "AdminChallengeStepResult")
    data class AdminChallengeStepResult(
        val id: UUID,
        val number: Int,
        val type: ChallengeStepType,
        val maxDrawdownType: ChallengeStepMaxDrawdownType,
        val dailyDrawdownType: ChallengeStepDailyDrawdownType,
        val dailyPauseType: ChallengeStepDailyPauseType,
        val leverage: Int,
        val inactivityPeriodDays: Int?,
        val minProfitableTradingDays: Int,
        val maxTradingDays: Int?,
        val groupMt5: String?,
        val groupTradeLocker: String?,
        val groupCTrader: String?,
        val groupDxTrade: String?,
        val consistencyTarget: Int?,
        val createdAt: Instant,
        val updatedAt: Instant,
        val settings: List<AdminChallengeStepSettingResult>,
    )

    @Schema(name = "AdminChallengeStepSettingResult")
    data class AdminChallengeStepSettingResult(
        val id: UUID,
        val type: ChallengeStepSettingType,
        val isVisibleInConfigurator: Boolean,
        val useMovementsFromOrder: Boolean,
        val movements: List<AdminChallengeStepSettingMovementResult>,
    )

    @Schema(name = "AdminChallengeStepSettingMovementResult")
    data class AdminChallengeStepSettingMovementResult(
        val id: UUID,
        val type: ChallengeStepSettingMovementType,
        val percentageValue: Int?,
        val listValue: String?,
        val movementPercentageValue: Int?,
        val movementAbsoluteValue: BigDecimal,
        val isDefault: Boolean,
    )
}
