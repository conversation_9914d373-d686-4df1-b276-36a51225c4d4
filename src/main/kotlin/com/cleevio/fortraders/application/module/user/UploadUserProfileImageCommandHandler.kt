package com.cleevio.fortraders.application.module.user

import com.cleevio.fortraders.application.common.command.CommandHandler
import com.cleevio.fortraders.application.common.constant.USER
import com.cleevio.fortraders.application.module.file.FileService
import com.cleevio.fortraders.application.module.file.port.output.FileStorageService
import com.cleevio.fortraders.application.module.user.command.UploadUserProfileImageCommand
import com.cleevio.fortraders.application.module.user.constant.UPDATE_USER
import com.cleevio.fortraders.domain.model.file.constant.FileType
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class UploadUserProfileImageCommandHandler(
    private val fileService: FileService,
    private val fileStorageService: FileStorageService,
    private val userFinderService: UserFinderService,
) : CommandHandler<UploadUserProfileImageCommand.Result, UploadUserProfileImageCommand> {
    override val command = UploadUserProfileImageCommand::class

    @Transactional
    @Lock(module = USER, lockName = UPDATE_USER)
    override fun invoke(
        @LockFieldParameter("userId") command: UploadUserProfileImageCommand,
    ): UploadUserProfileImageCommand.Result {
        val user = userFinderService.getById(command.userId)
        val profilePictureFile = fileService.upload(command.file, FileType.USER_PROFILE_PICTURE)

        user.profileImageFileId?.let { fileService.delete(it) }
        user.updateProfileImage(profilePictureFile.id)

        return UploadUserProfileImageCommand.Result(
            profileImageUrl = fileStorageService.getFileUrl(
                fileId = profilePictureFile.id,
                fileExtension = profilePictureFile.extension,
                fileType = profilePictureFile.type
            )
        )
    }
}
