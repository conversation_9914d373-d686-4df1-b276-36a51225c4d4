package com.cleevio.fortraders.application.module.tradingaccount

import com.cleevio.fortraders.application.common.query.QueryHandler
import com.cleevio.fortraders.application.module.tradingaccount.port.output.GetTradingAccount
import com.cleevio.fortraders.application.module.tradingaccount.query.GetTradingAccountQuery
import org.springframework.stereotype.Service

@Service
class GetTradingAccountQueryHandler(
    private val getTradingAccount: GetTradingAccount,
) : QueryHandler<GetTradingAccountQuery.Result, GetTradingAccountQuery> {
    override val query = GetTradingAccountQuery::class

    override fun handle(query: GetTradingAccountQuery): GetTradingAccountQuery.Result = getTradingAccount(
        userId = query.userId,
        tradingAccountId = query.tradingAccountId,
        statsFrom = query.statsFrom,
        statsTo = query.statsTo
    )
}
