package com.cleevio.fortraders.application.module.badge.query

import com.cleevio.fortraders.application.common.query.Query
import io.swagger.v3.oas.annotations.media.Schema

class AdminGetBadgesQuery : Query<AdminGetBadgesQuery.Result> {

    @Schema(name = "AdminGetBadgesQueryResult")
    data class Result(
        val requestedPayoutsCount: Int,
        val pendingWithdrawalRequestsCount: Int,
    )
}
