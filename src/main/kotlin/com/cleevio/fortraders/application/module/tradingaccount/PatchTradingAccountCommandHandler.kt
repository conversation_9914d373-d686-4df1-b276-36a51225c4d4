package com.cleevio.fortraders.application.module.tradingaccount

import com.cleevio.fortraders.application.common.command.CommandHandler
import com.cleevio.fortraders.application.common.constant.TRADING_ACCOUNT
import com.cleevio.fortraders.application.module.tradingaccount.command.PatchTradingAccountCommand
import com.cleevio.fortraders.application.module.tradingaccount.constant.UPDATE_TRADING_ACCOUNT
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class PatchTradingAccountCommandHandler(
    private val tradingAccountFinderService: TradingAccountFinderService,
) : CommandHandler<Unit, PatchTradingAccountCommand> {
    override val command = PatchTradingAccountCommand::class

    @Transactional
    @Lock(module = TRADING_ACCOUNT, lockName = UPDATE_TRADING_ACCOUNT)
    override fun invoke(@LockFieldParameter("tradingAccountId") command: PatchTradingAccountCommand) {
        tradingAccountFinderService.getByIdAndUserId(
            id = command.tradingAccountId,
            userId = command.userId
        ).patchUserSpecifiedProperties(
            hasPublicTradingJournal = command.hasPublicTradingJournal
        )
    }
}
