package com.cleevio.fortraders.application.module.challengeplan

import com.cleevio.fortraders.application.common.command.CommandHandler
import com.cleevio.fortraders.application.common.constant.CHALLENGE_PLAN
import com.cleevio.fortraders.application.module.challengeplan.command.AdminUpdateChallengePlanCommand
import com.cleevio.fortraders.application.module.challengeplan.constant.CREATE_OR_UPDATE_CHALLENGE_PLAN
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class AdminUpdateChallengePlanCommandHandler(
    private val challengePlanFinderService: ChallengePlanFinderService,
) : CommandHandler<Unit, AdminUpdateChallengePlanCommand> {
    override val command = AdminUpdateChallengePlanCommand::class

    @Transactional
    @Lock(module = CHALLENGE_PLAN, lockName = CREATE_OR_UPDATE_CHALLENGE_PLAN)
    override fun invoke(@LockFieldParameter("challengePlanId") command: AdminUpdateChallengePlanCommand) {
        challengePlanFinderService.getById(command.challengePlanId).update(
            externalProductId = command.externalProductId,
            title = command.title,
            basePrice = command.basePrice,
            platforms = command.platforms,
            isSubscription = command.isSubscription,
            capTrailingDrawdownAfterPayout = command.capTrailingDrawdownAfterPayout,
            minimumPayoutLimit = command.minimumPayoutLimit,
            maximumPayoutLimit = command.maximumPayoutLimit
        )
    }
}
