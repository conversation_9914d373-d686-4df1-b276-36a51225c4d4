package com.cleevio.fortraders.application.module.tradingaccount

import com.cleevio.fortraders.application.module.breach.BreachService
import com.cleevio.fortraders.domain.model.order.event.OrderCancellationReason
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class TradingAccountProcessingService(
    private val tradingAccountFinderService: TradingAccountFinderService,
    private val tradingAccountService: TradingAccountService,
    private val tradingAccountUpgradeService: TradingAccountUpgradeService,
    private val breachService: BreachService,
) {
    fun processTradingAccountAfterRecurringOrderPaymentCompleted(orderId: UUID) {
        tradingAccountFinderService.findPausedRecurringPaymentAttemptFailedByOrderId(orderId)?.let {
            tradingAccountService.reactivateRecurringPaymentAttemptFailedPausedAccount(it.id)
        }
    }

    fun processTradingAccountAfterFirstRecurringPaymentAttemptFailed(orderId: UUID) {
        tradingAccountFinderService.findNonBreachedIdByOrderId(orderId)?.let {
            tradingAccountService.pauseAfterFirstRecurringPaymentAttemptFailed(it)
        }
    }

    fun processTradingAccountAfterOrderCancel(orderId: UUID, reason: OrderCancellationReason) {
        tradingAccountFinderService.findNonBreachedIdByOrderId(orderId)?.let {
            when (reason) {
                OrderCancellationReason.PAYMENT_FAILED -> breachService.paymentFailedTradingAccountBreach(
                    tradingAccountId = it,
                    internalReason = null,
                    externalReason = "Payment failed"
                )

                OrderCancellationReason.ORDER_CANCELLED -> breachService.orderCancelledTradingAccountBreach(
                    tradingAccountId = it,
                    internalReason = null,
                    externalReason = "Order cancelled"
                )
            }
        }
    }

    fun processTradingAccountAfterContractSigned(orderId: UUID) {
        val tradingAccount = tradingAccountFinderService.getLatestByOrderId(orderId = orderId)
        tradingAccountUpgradeService.upgradeToNextStepIfPossible(
            tradingAccountId = tradingAccount.id
        )
    }
}
