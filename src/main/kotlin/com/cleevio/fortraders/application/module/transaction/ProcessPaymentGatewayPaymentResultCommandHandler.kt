package com.cleevio.fortraders.application.module.transaction

import com.cleevio.fortraders.application.common.command.CommandHandler
import com.cleevio.fortraders.application.common.constant.TRANSACTION
import com.cleevio.fortraders.application.module.transaction.command.ProcessPaymentGatewayPaymentResultCommand
import com.cleevio.fortraders.application.module.transaction.constant.PROCESS_PAYMENT_GATEWAY_PAYMENT_RESULT
import com.cleevio.fortraders.application.module.transaction.port.output.ParsePaymentGatewayPaymentResult
import com.cleevio.fortraders.application.module.transaction.port.output.forPaymentGatewayOrNull
import com.cleevio.fortraders.application.module.wallet.WalletFinderService
import com.cleevio.fortraders.domain.model.transaction.event.TransactionStatusChangedEvent
import com.cleevio.fortraders.infrastructure.config.logger
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class ProcessPaymentGatewayPaymentResultCommandHandler(
    private val paymentGatewayPaymentResultParsers: List<ParsePaymentGatewayPaymentResult>,
    private val transactionFinderService: TransactionFinderService,
    private val walletFinderService: WalletFinderService,
    private val transactionService: TransactionService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) : CommandHandler<Unit, ProcessPaymentGatewayPaymentResultCommand> {
    override val command = ProcessPaymentGatewayPaymentResultCommand::class
    private val logger = logger()

    @Transactional
    @Lock(module = TRANSACTION, lockName = PROCESS_PAYMENT_GATEWAY_PAYMENT_RESULT)
    override fun invoke(@LockFieldParameter("reference") command: ProcessPaymentGatewayPaymentResultCommand) {
        logger.info("Starting to process payment gateway payment result $command")

        val parser = paymentGatewayPaymentResultParsers.forPaymentGatewayOrNull(command.paymentGatewayProvider)
            ?: error("${command.paymentGatewayProvider} payment result parser not found")
        val paymentResult = parser(
            paymentReference = command.reference,
            result = command.result
        ) ?: return

        val transaction = transactionFinderService.getById(paymentResult.transactionId)
        if (transaction.willStatusChange(paymentResult.transactionStatus)) {
            if (transaction.type.isSubscription() && paymentResult.transactionStatus.isSuccess() && transaction.orderId != null) {
                // new transaction is subscription for order and was successful - cancel old subscription
                val wallet = walletFinderService.getById(transaction.walletId)
                transactionService.cancelSubscriptionTransactionIfExists(
                    userId = wallet.userId,
                    orderId = transaction.orderId
                )
            }

            transaction.changeStatus(paymentResult.transactionStatus)
            applicationEventPublisher.publishEvent(
                TransactionStatusChangedEvent(
                    transactionId = transaction.id,
                    walletId = transaction.walletId,
                    type = transaction.type,
                    newStatus = transaction.status
                )
            )
        }
    }
}
