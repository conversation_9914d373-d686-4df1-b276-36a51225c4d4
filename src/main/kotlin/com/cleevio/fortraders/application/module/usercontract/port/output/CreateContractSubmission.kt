package com.cleevio.fortraders.application.module.usercontract.port.output

import com.cleevio.fortraders.domain.model.contracttemplate.constant.ContractTemplateVariable

interface CreateContractSubmission {
    operator fun invoke(
        contractTemplateExternalId: Long,
        contractTemplateVariables: Map<ContractTemplateVariable, Any>,
        email: String,
    ): Result<CreateContractSubmissionModel>
}

data class CreateContractSubmissionModel(
    val submissionId: Long,
    val contractEmbedUrl: String,
)
