package com.cleevio.fortraders.application.module.notification

import com.cleevio.fortraders.application.module.notification.port.output.SendSuccessfulDepositInAppNotification
import com.cleevio.fortraders.application.module.notification.port.output.toEmailVariables
import com.cleevio.fortraders.application.module.transaction.TransactionFinderService
import com.cleevio.fortraders.application.module.wallet.WalletFinderService
import com.cleevio.fortraders.domain.model.mailtemplate.constant.MailType
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionStatus
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionType
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class TransactionNotificationProcessingService(
    private val transactionFinderService: TransactionFinderService,
    private val walletFinderService: WalletFinderService,
    private val sendSuccessfulDepositInAppNotification: SendSuccessfulDepositInAppNotification,
    private val mailNotificationService: MailNotificationService,
) {
    fun processTransaction(transactionId: UUID, transactionType: TransactionType, transactionStatus: TransactionStatus) {
        if (!transactionType.isUserDeposit()) return

        when (transactionStatus) {
            TransactionStatus.COMPLETED -> processTransactionIfSuccessfulDeposit(transactionId)
            TransactionStatus.ERROR -> processTransactionIfPaymentFailed(transactionId)
            else -> return
        }
    }

    private fun processTransactionIfSuccessfulDeposit(transactionId: UUID) {
        val transaction = transactionFinderService.getById(transactionId)
        val isOrderPaymentDepositTransaction = transaction.orderId != null
        val wallet = walletFinderService.getById(transaction.walletId)

        if (!isOrderPaymentDepositTransaction) {
            // do not send email notifications for deposits that are used for order payment
            mailNotificationService.sendEmailNotificationToUser(
                userId = wallet.userId,
                subjectId = transactionId,
                type = MailType.PAYMENT_SUCCESS,
                mailVariables = transaction.toEmailVariables()
            )
        }

        sendSuccessfulDepositInAppNotification(
            userId = wallet.userId,
            transactionId = transaction.id,
            amount = transaction.amount,
            type = transaction.type
        )
    }

    private fun processTransactionIfPaymentFailed(transactionId: UUID) {
        val transaction = transactionFinderService.getById(transactionId)
        val wallet = walletFinderService.getById(transaction.walletId)

        mailNotificationService.sendEmailNotificationToUser(
            userId = wallet.userId,
            subjectId = transactionId,
            type = MailType.PAYMENT_FAILED,
            mailVariables = transaction.toEmailVariables()
        )
    }
}
