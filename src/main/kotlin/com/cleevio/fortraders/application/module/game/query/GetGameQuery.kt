package com.cleevio.fortraders.application.module.game.query

import com.cleevio.fortraders.application.common.query.Query
import com.cleevio.fortraders.domain.model.game.constant.GameType
import com.cleevio.fortraders.domain.model.tournament.constant.TournamentPhase
import com.cleevio.fortraders.domain.model.tournament.constant.TournamentReward
import com.cleevio.fortraders.domain.model.tournamentuser.constant.TournamentUserState
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.time.Instant
import java.util.UUID

data class GetGameQuery(
    val gameId: UUID,
    val userId: UUID?,
) : Query<GetGameQuery.Result> {

    @Schema(name = "GetGameResult")
    data class Result(
        val id: UUID,
        val type: GameType,
        val name: String,
        val description: String,
        val logoUrl: String,
        val players: Int,
        val averageProfit: BigDecimal?,
        val tournaments: TournamentsDetail,
    )

    @Schema(name = "GetGameResultTournamentsDetail")
    data class TournamentsDetail(
        val active: List<TournamentDetail>,
        val past: List<TournamentDetail>,
    )

    @Schema(name = "GetGameResultTournamentDetail")
    data class TournamentDetail(
        val id: UUID,
        val name: String,
        val reward: TournamentReward,
        val initialPrizePool: BigDecimal?,
        val entryFee: BigDecimal,
        val startsAt: Instant,
        val endsAt: Instant,
        val leverageEnabled: Boolean,
        val coverUrl: String,
        val phase: TournamentPhase,
        val buyInsLimitExceeded: Boolean,
        val user: UserDetail?,
    )

    @Schema(name = "GetGameResultUserDetail")
    data class UserDetail(
        val profit: BigDecimal?,
        val state: TournamentUserState,
    )
}
