package com.cleevio.fortraders.application.module.verificationcall

import com.cleevio.fortraders.application.common.command.CommandHandler
import com.cleevio.fortraders.application.common.constant.VERIFICATION_CALL
import com.cleevio.fortraders.application.module.order.OrderFinderService
import com.cleevio.fortraders.application.module.payout.PayoutFinderService
import com.cleevio.fortraders.application.module.tradingaccount.TradingAccountFinderService
import com.cleevio.fortraders.application.module.verificationcall.command.AdminCreateVerificationCallCommand
import com.cleevio.fortraders.application.module.verificationcall.constant.CREATE_VERIFICATION_CALL
import com.cleevio.fortraders.application.module.verificationcalluser.VerificationCallUserService
import com.cleevio.fortraders.domain.model.verificationcall.VerificationCallCreateService
import com.cleevio.fortraders.domain.model.verificationcall.event.VerificationCallRequestedEvent
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class AdminCreateVerificationCallCommandHandler(
    private val verificationCallCreateService: VerificationCallCreateService,
    private val applicationEventPublisher: ApplicationEventPublisher,
    private val tradingAccountFinderService: TradingAccountFinderService,
    private val payoutFinderService: PayoutFinderService,
    private val orderFinderService: OrderFinderService,
    private val verificationCallUserService: VerificationCallUserService,
) : CommandHandler<Unit, AdminCreateVerificationCallCommand> {
    override val command = AdminCreateVerificationCallCommand::class

    @Transactional
    @Lock(module = VERIFICATION_CALL, lockName = CREATE_VERIFICATION_CALL)
    override fun invoke(@LockFieldParameter("tradingAccountId") command: AdminCreateVerificationCallCommand) {
        command.payoutId?.let {
            payoutFinderService.validateIfExistsByIdAndTradingAccountId(
                id = it,
                tradingAccountId = command.tradingAccountId
            )
        }

        val userId = tradingAccountFinderService.getById(command.tradingAccountId).let {
            orderFinderService.getById(it.orderId).userId
        }

        val verificationCall = verificationCallCreateService.create(
            type = command.type,
            tradingAccountId = command.tradingAccountId,
            payoutId = command.payoutId,
            note = command.note,
            emailContent = command.emailContent
        )

        command.supportUserIds?.let { supportUserIds ->
            verificationCallUserService.createOrUpdateVerificationCallUsers(
                verificationCallId = verificationCall.id,
                supportUsers = supportUserIds
            )
        }

        applicationEventPublisher.publishEvent(
            VerificationCallRequestedEvent(
                verificationCallId = verificationCall.id,
                userId = userId
            )
        )
    }
}
