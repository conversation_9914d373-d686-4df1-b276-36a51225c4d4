package com.cleevio.fortraders.application.common.dto

import io.swagger.v3.oas.annotations.media.Schema
import org.hibernate.envers.RevisionType
import java.time.Instant
import java.util.UUID

@Schema(name = "AuditRevisionResult")
data class AuditRevisionResult(
    val id: Long,
    val revisionType: RevisionType,
    val createdAt: Instant,
    val createdBy: UserDetail?,
    val fieldChanges: List<ChangeDetail>,
) {
    @Schema(name = "AuditRevisionResultChangeDetail")
    data class ChangeDetail(
        val fieldName: String,
        val oldValue: Any?,
        val newValue: Any?,
    )

    @Schema(name = "AuditRevisionResultUserDetail")
    data class UserDetail(
        val id: UUID,
        val email: String,
    )
}
