package com.cleevio.fortraders.application.common.validation

import jakarta.validation.Constraint
import jakarta.validation.ConstraintValidator
import jakarta.validation.ConstraintValidatorContext
import jakarta.validation.Payload
import org.apache.commons.validator.routines.InetAddressValidator
import kotlin.reflect.KClass

@Constraint(validatedBy = [IPValidator::class])
@Target(AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
annotation class IP(
    val message: String = "must be a well-formed IP address",
    val groups: Array<KClass<*>> = [],
    val payload: Array<KClass<out Payload>> = [],
)

class IPValidator : ConstraintValidator<IP, String> {
    // considers null as valid
    override fun isValid(value: String?, context: ConstraintValidatorContext?) = value?.let {
        InetAddressValidator.getInstance().isValid(it)
    } ?: true
}
