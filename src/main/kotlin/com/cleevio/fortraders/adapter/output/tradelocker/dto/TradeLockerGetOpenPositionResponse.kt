package com.cleevio.fortraders.adapter.output.tradelocker.dto

import com.fasterxml.jackson.annotation.JsonProperty
import java.math.BigDecimal

data class TradeLockerGetOpenPositionResponse(
    @JsonProperty("positionId") val positionId: String,
    @JsonProperty("accountId") val accountId: String,
    @JsonProperty("pnl") val profit: BigDecimal,
    @JsonProperty("currentPrice") val price: BigDecimal,
)
