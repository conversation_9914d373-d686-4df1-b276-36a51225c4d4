package com.cleevio.fortraders.adapter.output.veriff

import com.cleevio.fortraders.adapter.output.BaseConnector
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class VeriffConnector(
    @Value("\${integration.veriff.base-url}")
    baseUrl: String,

    @Value("\${integration.veriff.api-key}")
    apiKey: String,
) : BaseConnector(
    baseUrl = baseUrl,
    restClientCustomizer = {
        it.defaultHeader(API_KEY_HEADER, apiKey)
    }
) {
    fun requestVerification(userId: UUID): String = restClient.post()
        .uriAndHeaders("/v1/sessions")
        .jsonBody(
            VeriffRequestVerificationRequest(
                verification = VerificationData(
                    vendorData = userId
                )
            )
        )
        .retrieveResponseWithErrorHandler<VeriffRequestVerificationResponse>()
        .verification.url
}

private data class VeriffRequestVerificationRequest(
    val verification: VerificationData,
)

private data class VerificationData(
    val vendorData: UUID,
)

private data class VeriffRequestVerificationResponse(
    val status: String,
    val verification: VerificationResponse,
)

private data class VerificationResponse(
    val id: String,
    val url: String,
)

private const val API_KEY_HEADER = "X-AUTH-CLIENT"
