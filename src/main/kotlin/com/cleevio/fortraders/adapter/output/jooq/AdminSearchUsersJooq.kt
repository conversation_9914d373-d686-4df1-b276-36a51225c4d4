package com.cleevio.fortraders.adapter.output.jooq

import com.cleevio.fortraders.application.common.util.trimOrNull
import com.cleevio.fortraders.application.module.user.port.output.AdminSearchUsers
import com.cleevio.fortraders.application.module.user.query.AdminSearchUsersQuery
import com.cleevio.fortraders.domain.model.user.constant.UserRole
import com.cleevio.fortraders.domain.model.user.constant.UserState
import com.cleevio.fortraders.public.tables.references.LABEL
import com.cleevio.fortraders.public.tables.references.USER
import org.jooq.DSLContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.util.UUID

@Service
class AdminSearchUsersJooq(private val context: DSLContext) : AdminSearchUsers {

    @Transactional(readOnly = true)
    override fun invoke(
        pageable: Pageable,
        states: Set<UserState>?,
        createdAtFrom: Instant?,
        createdAtTo: Instant?,
        labels: Set<UUID>?,
        fulltext: String?,
        userRoles: Set<UserRole>?,
    ): Page<AdminSearchUsersQuery.Result> {
        val filterConditions = listOfNotNull(
            USER.STATE.inOrNullIfNullOrEmpty(states),
            USER.ROLE.inOrNullIfNullOrEmpty(userRoles),
            createdAtFrom?.let { USER.CREATED_AT.greaterOrEqual(it) },
            createdAtTo?.let { USER.CREATED_AT.lessOrEqual(it) },
            USER.LABELS.overlapIfNotEmptyOrNull(labels),
            fulltext.trimOrNull()?.let {
                USER.FULL_NAME.likeIgnoreCaseWithPattern(it)
                    .or(USER.EMAIL.likeIgnoreCaseWithPattern(it))
            }
        )

        val labels = USER.createLabelsMultiset()

        val count = context.fetchCount(USER, filterConditions)

        return context
            .select(
                USER.ID,
                USER.ROLE,
                USER.EMAIL,
                USER.FIRST_NAME,
                USER.LAST_NAME,
                USER.STATE,
                USER.PREFERRED_LANGUAGE,
                USER.CREATED_AT,
                USER.UPDATED_AT,
                USER.LABELS,
                labels
            )
            .from(USER)
            .where(filterConditions)
            .sort(pageable.sort, USER)
            .page(pageable)
            .fetch()
            .map {
                AdminSearchUsersQuery.Result(
                    id = it[USER.ID]!!,
                    role = it[USER.ROLE],
                    email = it[USER.EMAIL]!!,
                    firstName = it[USER.FIRST_NAME],
                    lastName = it[USER.LAST_NAME],
                    state = it[USER.STATE]!!,
                    preferredLanguage = it[USER.PREFERRED_LANGUAGE]!!,
                    createdAt = it[USER.CREATED_AT]!!,
                    updatedAt = it[USER.UPDATED_AT]!!,
                    labels = it[labels].map { label ->
                        AdminSearchUsersQuery.LabelDetail(
                            id = label[LABEL.ID]!!,
                            name = label[LABEL.NAME]!!
                        )
                    }
                )
            }.let {
                PageImpl(it, pageable, count.toLong())
            }
    }
}
