package com.cleevio.fortraders.adapter.output.jooq

import com.cleevio.fortraders.application.module.file.port.output.FileToUrlMapper
import com.cleevio.fortraders.application.module.withdrawalrequest.port.output.AdminGetWithdrawalRequestDetails
import com.cleevio.fortraders.application.module.withdrawalrequest.query.AdminGetWithdrawalRequestDetailsQuery
import com.cleevio.fortraders.domain.model.withdrawalrequest.constant.WithdrawalRequestStatus
import com.cleevio.fortraders.domain.model.withdrawalrequest.exception.WithdrawalRequestNotFoundException
import com.cleevio.fortraders.public.tables.references.WITHDRAWAL_REQUEST
import org.jooq.DSLContext
import org.jooq.impl.DSL.field
import org.jooq.impl.DSL.select
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class AdminGetWithdrawalRequestDetailsJooq(private val context: DSLContext) : AdminGetWithdrawalRequestDetails {

    @Transactional(readOnly = true)
    override fun invoke(
        withdrawalRequestId: UUID,
        fileToUrlMapper: FileToUrlMapper,
    ): AdminGetWithdrawalRequestDetailsQuery.Result {
        val sumOfWithdrawals = field(
            select(sumOrZero(WITHDRAWAL_REQUEST.AMOUNT_AFTER_FEE))
                .from(WITHDRAWAL_REQUEST)
                .where(
                    WITHDRAWAL_REQUEST.USER_ID.eq(WITHDRAWAL_REQUEST.user.ID)
                        .and(WITHDRAWAL_REQUEST.STATUS.eq(WithdrawalRequestStatus.APPROVED))
                )
        )

        val isUserEmailOrderBlacklisted = isUserEmailOrderBlacklisted(WITHDRAWAL_REQUEST.user.EMAIL)

        return context
            .select(
                WITHDRAWAL_REQUEST.ID,
                WITHDRAWAL_REQUEST.user.ID,
                WITHDRAWAL_REQUEST.user.EMAIL,
                WITHDRAWAL_REQUEST.AMOUNT_AFTER_FEE,
                WITHDRAWAL_REQUEST.TYPE,
                WITHDRAWAL_REQUEST.ACCOUNT_HOLDER_FULL_NAME,
                WITHDRAWAL_REQUEST.BANK_NAME,
                WITHDRAWAL_REQUEST.BANK_ADDRESS,
                WITHDRAWAL_REQUEST.IBAN,
                WITHDRAWAL_REQUEST.SWIFT,
                WITHDRAWAL_REQUEST.ERC20_ADDRESS,
                WITHDRAWAL_REQUEST.EMAIL,
                WITHDRAWAL_REQUEST.STATUS,
                WITHDRAWAL_REQUEST.REJECT_REASON,
                WITHDRAWAL_REQUEST.CREATED_AT,
                WITHDRAWAL_REQUEST.UPDATED_AT,
                sumOfWithdrawals,
                WITHDRAWAL_REQUEST.user.contact.PHONE_PREFIX,
                WITHDRAWAL_REQUEST.user.contact.PHONE_NUMBER,
                WITHDRAWAL_REQUEST.user.FIRST_NAME,
                WITHDRAWAL_REQUEST.user.LAST_NAME,
                WITHDRAWAL_REQUEST.user.file.ID,
                WITHDRAWAL_REQUEST.user.file.EXTENSION,
                WITHDRAWAL_REQUEST.user.file.TYPE,
                isUserEmailOrderBlacklisted
            )
            .from(WITHDRAWAL_REQUEST)
            .join(WITHDRAWAL_REQUEST.user)
            .leftJoin(WITHDRAWAL_REQUEST.user.contact)
            .leftJoin(WITHDRAWAL_REQUEST.user.file)
            .where(WITHDRAWAL_REQUEST.ID.eq(withdrawalRequestId))
            .fetchOne()
            ?.map {
                AdminGetWithdrawalRequestDetailsQuery.Result(
                    id = it[WITHDRAWAL_REQUEST.ID]!!,
                    amount = it[WITHDRAWAL_REQUEST.AMOUNT_AFTER_FEE]!!,
                    type = it[WITHDRAWAL_REQUEST.TYPE]!!,
                    accountHolderFullName = it[WITHDRAWAL_REQUEST.ACCOUNT_HOLDER_FULL_NAME],
                    bankName = it[WITHDRAWAL_REQUEST.BANK_NAME],
                    bankAddress = it[WITHDRAWAL_REQUEST.BANK_ADDRESS],
                    iban = it[WITHDRAWAL_REQUEST.IBAN],
                    swift = it[WITHDRAWAL_REQUEST.SWIFT],
                    erc20Address = it[WITHDRAWAL_REQUEST.ERC20_ADDRESS],
                    riseEmail = it[WITHDRAWAL_REQUEST.EMAIL],
                    status = it[WITHDRAWAL_REQUEST.STATUS]!!,
                    rejectReason = it[WITHDRAWAL_REQUEST.REJECT_REASON],
                    createdAt = it[WITHDRAWAL_REQUEST.CREATED_AT]!!,
                    updatedAt = it[WITHDRAWAL_REQUEST.UPDATED_AT]!!,
                    sumOfWithdrawals = it[sumOfWithdrawals],
                    user = AdminGetWithdrawalRequestDetailsQuery.UserDetails(
                        id = it[WITHDRAWAL_REQUEST.user.ID]!!,
                        email = it[WITHDRAWAL_REQUEST.user.EMAIL]!!,
                        phonePrefix = it[WITHDRAWAL_REQUEST.user.contact.PHONE_PREFIX],
                        phoneNumber = it[WITHDRAWAL_REQUEST.user.contact.PHONE_NUMBER],
                        firstName = it[WITHDRAWAL_REQUEST.user.FIRST_NAME],
                        lastName = it[WITHDRAWAL_REQUEST.user.LAST_NAME],
                        profileImageUrl = it[WITHDRAWAL_REQUEST.user.file.ID]?.let { _ ->
                            fileToUrlMapper(
                                it[WITHDRAWAL_REQUEST.user.file.ID]!!,
                                it[WITHDRAWAL_REQUEST.user.file.EXTENSION]!!,
                                it[WITHDRAWAL_REQUEST.user.file.TYPE]!!
                            )
                        },
                        blacklisted = it[isUserEmailOrderBlacklisted]!!
                    )
                )
            } ?: throw WithdrawalRequestNotFoundException()
    }
}
