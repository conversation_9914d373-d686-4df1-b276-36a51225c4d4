package com.cleevio.fortraders.adapter.output.mt5

import com.cleevio.fortraders.application.module.challengeplan.port.output.AdminGetTradingGroups
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service

@Service
@Profile("!test")
class AdminGetMT5TradingGroupsService(
    private val mt5Connector: MT5Connector,
) : AdminGetTradingGroups {
    override val supportedPlatform = PlatformType.META_TRADER_5

    override fun invoke(): List<String> = mt5Connector.getGroups()
}
