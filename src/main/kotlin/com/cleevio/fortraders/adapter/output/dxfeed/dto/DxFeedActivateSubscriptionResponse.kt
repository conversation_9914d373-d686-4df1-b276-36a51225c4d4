package com.cleevio.fortraders.adapter.output.dxfeed.dto

import com.fasterxml.jackson.annotation.JsonProperty
import java.util.UUID

data class DxFeedActivateSubscriptionResponse(
    val signupPageByTheme: DxFeedActivateSubscriptionSignupPageDetailResponse?,
    val confirmationId: UUID?,
)

data class DxFeedActivateSubscriptionSignupPageDetailResponse(
    @JsonProperty("LIGHT") val light: String,
    @JsonProperty("DARK") val dark: String,
)
