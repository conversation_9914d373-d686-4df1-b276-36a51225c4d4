package com.cleevio.fortraders.adapter.output.jooq

import com.cleevio.fortraders.application.common.util.trimOrNull
import com.cleevio.fortraders.application.module.order.port.output.AdminSearchOrders
import com.cleevio.fortraders.application.module.order.query.AdminSearchOrdersQuery
import com.cleevio.fortraders.domain.model.challenge.constant.ChallengeType
import com.cleevio.fortraders.domain.model.order.constant.OrderState
import com.cleevio.fortraders.public.tables.references.ORDER
import org.jooq.DSLContext
import org.jooq.impl.DSL.or
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.util.UUID

@Service
class AdminSearchOrdersJooq(private val context: DSLContext) : AdminSearchOrders {

    @Transactional(readOnly = true)
    override fun invoke(
        pageable: Pageable,
        paidOnly: <PERSON><PERSON><PERSON>,
        states: Set<OrderState>?,
        createdAtFrom: Instant?,
        createdAtTo: Instant?,
        userEmails: Set<String>?,
        userIds: Set<UUID>?,
        challengeTypes: Set<ChallengeType>?,
        fulltext: String?,
    ): Page<AdminSearchOrdersQuery.Result> {
        val filterConditions = listOfNotNull(
            if (paidOnly) ORDER.PAID_AT.isNotNull else null,
            ORDER.STATE.inOrNullIfNullOrEmpty(states),
            createdAtFrom?.let { ORDER.CREATED_AT.greaterOrEqual(it) },
            createdAtTo?.let { ORDER.CREATED_AT.lessOrEqual(it) },
            ORDER.user.EMAIL.inOrNullIfNullOrEmpty(userEmails),
            ORDER.user.ID.inOrNullIfNullOrEmpty(userIds),
            ORDER.challenge.TYPE.inOrNullIfNullOrEmpty(challengeTypes),
            fulltext.trimOrNull()?.let {
                or(
                    ORDER.user.FULL_NAME.likeIgnoreCaseWithPattern(it),
                    ORDER.user.EMAIL.likeIgnoreCaseWithPattern(it),
                    ORDER.ID.likeIgnoreCaseWithPattern(it),
                    ORDER.PRODUCT_NAME.likeIgnoreCaseWithPattern(it),
                    ORDER.affiliate.COUPON_CODE.likeIgnoreCaseWithPattern(it),
                    ORDER.discountCode.CODE.likeIgnoreCaseWithPattern(it)
                )
            }
        )

        val count = context.fetchCount(ORDER, filterConditions)
        val isUserEmailOrderBlacklisted = isUserEmailOrderBlacklisted(ORDER.user.EMAIL)

        return context.select(
            ORDER.ID,
            ORDER.STATE,
            ORDER.PRICE,
            ORDER.CREATED_AT,
            ORDER.PRODUCT_NAME,
            ORDER.user.ID,
            ORDER.user.EMAIL,
            ORDER.user.FIRST_NAME,
            ORDER.user.LAST_NAME,
            ORDER.discountCode.ID,
            ORDER.discountCode.CODE,
            ORDER.discountCode.DESCRIPTION,
            ORDER.discountCode.VALID_FROM,
            ORDER.discountCode.VALID_UNTIL,
            ORDER.affiliate.ID,
            ORDER.affiliate.COUPON_CODE,
            ORDER.challenge.TYPE,
            isUserEmailOrderBlacklisted
        )
            .from(ORDER)
            .join(ORDER.user).onKey()
            .join(ORDER.challenge).onKey()
            .join(ORDER.challengePlan).onKey()
            .leftJoin(ORDER.discountCode).onKey()
            .leftJoin(ORDER.affiliate).onKey()
            .where(filterConditions)
            .sort(pageable.sort, ORDER)
            .page(pageable)
            .fetch()
            .map {
                AdminSearchOrdersQuery.Result(
                    id = it[ORDER.ID]!!,
                    state = it[ORDER.STATE]!!,
                    price = it[ORDER.PRICE]!!,
                    createdAt = it[ORDER.CREATED_AT]!!,
                    productName = it[ORDER.PRODUCT_NAME],
                    challengeType = it[ORDER.challenge.TYPE]!!,
                    user = AdminSearchOrdersQuery.UserDetail(
                        id = it[ORDER.user.ID]!!,
                        email = it[ORDER.user.EMAIL]!!,
                        firstName = it[ORDER.user.FIRST_NAME],
                        lastName = it[ORDER.user.LAST_NAME],
                        blacklisted = it[isUserEmailOrderBlacklisted]!!
                    ),
                    discountCode = it[ORDER.discountCode.ID]?.let { _ ->
                        AdminSearchOrdersQuery.DiscountCodeDetail(
                            id = it[ORDER.discountCode.ID]!!,
                            code = it[ORDER.discountCode.CODE]!!,
                            description = it[ORDER.discountCode.DESCRIPTION]!!,
                            validFrom = it[ORDER.discountCode.VALID_FROM]!!,
                            validUntil = it[ORDER.discountCode.VALID_UNTIL]
                        )
                    },
                    affiliateCode = it[ORDER.affiliate.ID]?.let { _ ->
                        AdminSearchOrdersQuery.AffiliateCodeDetail(
                            id = it[ORDER.affiliate.ID]!!,
                            code = it[ORDER.affiliate.COUPON_CODE]!!
                        )
                    }
                )
            }
            .let {
                PageImpl(it, pageable, count.toLong())
            }
    }
}
