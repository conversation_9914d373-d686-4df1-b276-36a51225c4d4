package com.cleevio.fortraders.adapter.output.veriff

import com.cleevio.fortraders.application.module.user.port.output.RequestUserKycVerification
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service
import java.util.UUID

@Service
@ConditionalOnProperty("fortraders.kyc.provider", havingValue = "VERIFF")
class RequestVeriffUserKycVerificationService(
    private val veriffConnector: VeriffConnector,
) : RequestUserKycVerification {
    override fun invoke(userId: UUID): String = veriffConnector.requestVerification(userId)
}
