package com.cleevio.fortraders.adapter.output.mt5

import com.cleevio.fortraders.application.module.tradingaccount.port.output.CreateTradingAccount
import com.cleevio.fortraders.application.module.tradingaccount.port.output.dto.CreateTradingAccountResult
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.util.UUID

@Service
@Profile("!test")
class CreateMT5AccountService(
    private val mt5Connector: MT5Connector,
) : CreateTradingAccount {
    override val supportedPlatform = PlatformType.META_TRADER_5

    override fun invoke(
        suggestedAccountId: String?,
        email: String,
        name: String,
        firstName: String?,
        lastName: String?,
        password: String,
        leverage: Int,
        tradingGroup: String,
        startingBalance: BigDecimal,
        feedAccountId: UUID?,
    ) = runCatching {
        val accountId = mt5Connector.createAccount(
            login = requireNotNull(suggestedAccountId),
            name = name,
            password = password,
            leverage = leverage,
            comment = null,
            tradingGroup = tradingGroup
        )
        mt5Connector.deposit(
            accountId = accountId,
            amount = startingBalance,
            comment = null
        )

        return@runCatching CreateTradingAccountResult(accountId = accountId)
    }
}
