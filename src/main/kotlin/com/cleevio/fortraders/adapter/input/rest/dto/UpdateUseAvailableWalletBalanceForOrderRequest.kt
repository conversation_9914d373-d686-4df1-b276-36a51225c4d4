package com.cleevio.fortraders.adapter.input.rest.dto

import com.cleevio.fortraders.application.module.order.command.UpdateUseAvailableWalletBalanceForOrderCommand
import java.util.UUID

data class UpdateUseAvailableWalletBalanceForOrderRequest(
    val shouldUse: <PERSON><PERSON><PERSON>,
) {
    fun toCommand(userId: UUID, orderId: UUID) = UpdateUseAvailableWalletBalanceForOrderCommand(
        userId = userId,
        orderId = orderId,
        shouldUse = shouldUse
    )
}
