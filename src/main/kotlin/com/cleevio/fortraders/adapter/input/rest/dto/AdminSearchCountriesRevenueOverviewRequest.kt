package com.cleevio.fortraders.adapter.input.rest.dto

import com.cleevio.fortraders.application.module.report.query.AdminSearchCountriesRevenueOverviewQuery
import com.cleevio.fortraders.domain.model.challenge.constant.ChallengeType
import java.time.LocalDate

data class AdminSearchCountriesRevenueOverviewRequest(
    val dateFrom: LocalDate?,
    val dateTo: LocalDate?,
    val challengeTypes: Set<ChallengeType>?,
) {
    fun toFilter() = AdminSearchCountriesRevenueOverviewQuery.Filter(
        dateFrom = dateFrom,
        dateTo = dateTo,
        challengeTypes = challengeTypes
    )
}
