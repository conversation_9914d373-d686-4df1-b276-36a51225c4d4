package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.adapter.input.rest.dto.CreateBankWithdrawalRequest
import com.cleevio.fortraders.adapter.input.rest.dto.CreateCryptoWithdrawalRequest
import com.cleevio.fortraders.adapter.input.rest.dto.CreateRiseWithdrawalRequest
import com.cleevio.fortraders.adapter.input.rest.dto.SearchUserWithdrawalRequestsRequest
import com.cleevio.fortraders.application.common.command.CommandBus
import com.cleevio.fortraders.application.common.dto.toSimplePage
import com.cleevio.fortraders.application.common.query.QueryBus
import com.cleevio.fortraders.application.module.withdrawalrequest.query.SearchUserWithdrawalRequestsQuery
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.core.annotations.ParameterObject
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpStatus
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "User withdrawal requests")
@RestController
@RequestMapping("/web-app/users/me/withdrawal-requests")
class UserWithdrawalRequestsController(
    private val commandBus: CommandBus,
    private val queryBus: QueryBus,
) {
    @PostMapping("/bank", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun createBankWithdrawal(@AuthenticationPrincipal userId: UUID, @RequestBody request: CreateBankWithdrawalRequest): Unit =
        commandBus(request.toCommand(userId))

    @PostMapping("/crypto", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun createCryptoWithdrawal(@AuthenticationPrincipal userId: UUID, @RequestBody request: CreateCryptoWithdrawalRequest): Unit =
        commandBus(request.toCommand(userId))

    @PostMapping("/rise", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun createRiseWithdrawal(@AuthenticationPrincipal userId: UUID, @RequestBody request: CreateRiseWithdrawalRequest): Unit =
        commandBus(request.toCommand(userId))

    @PostMapping("/search", produces = [ApiVersion.VERSION_1_JSON])
    fun searchUserWithdrawalRequests(
        @AuthenticationPrincipal userId: UUID,
        @ParameterObject @PageableDefaultSort pageable: Pageable,
        @RequestBody request: SearchUserWithdrawalRequestsRequest,
    ) = queryBus(
        SearchUserWithdrawalRequestsQuery(pageable = pageable, filter = request.toFilter(userId))
    ).toSimplePage()
}
