package com.cleevio.fortraders.adapter.input.rest.dto

import com.cleevio.fortraders.application.module.order.query.AdminSearchOrdersQuery
import com.cleevio.fortraders.domain.model.challenge.constant.ChallengeType
import com.cleevio.fortraders.domain.model.order.constant.OrderState
import java.time.Instant
import java.util.UUID

data class AdminSearchOrdersRequest(
    val paidOnly: Boolean?,
    val states: Set<OrderState>?,
    val createdAtFrom: Instant?,
    val createdAtTo: Instant?,
    val userEmails: Set<String>?,
    val userIds: Set<UUID>?,
    val fulltext: String?,
    val challengeTypes: Set<ChallengeType>?,
) {
    fun toFilter() = AdminSearchOrdersQuery.Filter(
        paidOnly = paidOnly ?: false,
        states = states,
        createdAtFrom = createdAtFrom,
        createdAtTo = createdAtTo,
        userEmails = userEmails,
        userIds = userIds,
        fulltext = fulltext,
        challengeTypes = challengeTypes
    )
}
