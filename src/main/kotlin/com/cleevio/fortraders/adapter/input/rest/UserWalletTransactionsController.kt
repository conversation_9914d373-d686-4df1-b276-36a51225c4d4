package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.adapter.input.rest.dto.CreateWalletDepositTransactionRequest
import com.cleevio.fortraders.adapter.input.rest.dto.SearchUserTransactionsRequest
import com.cleevio.fortraders.application.common.command.CommandBus
import com.cleevio.fortraders.application.common.dto.toSimplePage
import com.cleevio.fortraders.application.common.query.QueryBus
import com.cleevio.fortraders.application.module.transaction.command.CreateWalletDepositTransactionCommand
import com.cleevio.fortraders.application.module.transaction.query.GetUserTransactionQuery
import com.cleevio.fortraders.application.module.transaction.query.SearchUserTransactionsQuery
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.core.annotations.ParameterObject
import org.springframework.data.domain.Pageable
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "User wallet transactions")
@RestController
@RequestMapping("/web-app/users/me/wallet/transactions")
class UserWalletTransactionsController(
    private val commandBus: CommandBus,
    private val queryBus: QueryBus,
) {
    @GetMapping("/{transactionId}", produces = [ApiVersion.VERSION_1_JSON])
    fun getUserTransaction(
        @AuthenticationPrincipal userId: UUID,
        @PathVariable transactionId: UUID,
    ): GetUserTransactionQuery.Result = queryBus(GetUserTransactionQuery(userId, transactionId))

    @PostMapping(produces = [ApiVersion.VERSION_1_JSON])
    fun createWalletDepositTransaction(
        @AuthenticationPrincipal userId: UUID,
        @RequestBody request: CreateWalletDepositTransactionRequest,
    ): CreateWalletDepositTransactionCommand.Result = commandBus(request.toCommand(userId))

    @PostMapping("/search", produces = [ApiVersion.VERSION_1_JSON])
    fun searchUserTransactions(
        @AuthenticationPrincipal userId: UUID,
        @ParameterObject @PageableDefaultSort pageable: Pageable,
        @RequestBody request: SearchUserTransactionsRequest,
    ) = queryBus(
        SearchUserTransactionsQuery(pageable = pageable, filter = request.toFilter(userId))
    ).toSimplePage()
}
