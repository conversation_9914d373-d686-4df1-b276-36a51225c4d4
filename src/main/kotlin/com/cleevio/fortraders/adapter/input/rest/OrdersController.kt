package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.adapter.input.rest.dto.CreateOrderRequest
import com.cleevio.fortraders.adapter.input.rest.dto.UpdateUseAvailableWalletBalanceForOrderRequest
import com.cleevio.fortraders.application.common.command.CommandBus
import com.cleevio.fortraders.application.common.util.COUNTRY_ISO_CODE_HEADER_NAME
import com.cleevio.fortraders.application.common.util.normalizeLocationHeader
import com.cleevio.fortraders.application.module.order.command.ApplyAffiliateCodeToOrderCommand
import com.cleevio.fortraders.application.module.order.command.ApplyDiscountCodeToOrderCommand
import com.cleevio.fortraders.application.module.order.command.CancelOrderSubscriptionCommand
import com.cleevio.fortraders.application.module.order.command.CreateOrderCommand
import com.cleevio.fortraders.application.module.order.command.PayForFreeOrderCommand
import com.cleevio.fortraders.application.module.order.command.PayOrderCommand
import com.cleevio.fortraders.application.module.order.command.PayOrderFromWalletCommand
import com.cleevio.fortraders.application.module.order.command.UpdateUseAvailableWalletBalanceForOrderCommand
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionPaymentMethod
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.HttpStatus
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestHeader
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID
import org.springframework.web.bind.annotation.RequestParam

@Tag(name = "Orders")
@RestController
@RequestMapping("/web-app/orders")
class OrdersController(
    private val commandBus: CommandBus,
) {
    @PostMapping(produces = [ApiVersion.VERSION_1_JSON])
    fun createOrder(
        @AuthenticationPrincipal userId: UUID,
        @RequestBody request: CreateOrderRequest,
        @RequestHeader(COUNTRY_ISO_CODE_HEADER_NAME, required = false) cfCountryIsoCode: String?,
    ): CreateOrderCommand.Result = commandBus(
        request.toCommand(
            userId = userId,
            countryIsoCode = cfCountryIsoCode.normalizeLocationHeader()
        )
    )

    @PostMapping("/{orderId}/cancel-subscription", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun cancelOrderSubscription(@AuthenticationPrincipal userId: UUID, @PathVariable orderId: UUID): Unit =
        commandBus(CancelOrderSubscriptionCommand(userId = userId, orderId = orderId))

    @PostMapping("/{orderId}/use-wallet-balance", produces = [ApiVersion.VERSION_1_JSON])
    fun updateUseAvailableWalletBalance(
        @AuthenticationPrincipal userId: UUID,
        @PathVariable orderId: UUID,
        @RequestBody request: UpdateUseAvailableWalletBalanceForOrderRequest,
    ): UpdateUseAvailableWalletBalanceForOrderCommand.Result = commandBus(request.toCommand(userId = userId, orderId = orderId))

    @PostMapping("/{orderId}/discount-code/{discountCode}", produces = [ApiVersion.VERSION_1_JSON])
    fun applyDiscountCodeToOrder(
        @AuthenticationPrincipal userId: UUID,
        @PathVariable orderId: UUID,
        @PathVariable discountCode: String,
        @RequestHeader(COUNTRY_ISO_CODE_HEADER_NAME, required = false) cfCountryIsoCode: String?,
    ): ApplyDiscountCodeToOrderCommand.Result = commandBus(
        ApplyDiscountCodeToOrderCommand(
            code = discountCode.uppercase(),
            orderId = orderId,
            userId = userId,
            countryIsoCode = cfCountryIsoCode.normalizeLocationHeader()
        )
    )

    @PostMapping("/{orderId}/affiliate-code/{affiliateCode}", produces = [ApiVersion.VERSION_1_JSON])
    fun applyAffiliateCodeToOrder(
        @AuthenticationPrincipal userId: UUID,
        @PathVariable orderId: UUID,
        @PathVariable affiliateCode: String,
    ): ApplyAffiliateCodeToOrderCommand.Result = commandBus(
        ApplyAffiliateCodeToOrderCommand(
            code = affiliateCode.uppercase(),
            orderId = orderId,
            userId = userId
        )
    )

    @PostMapping("/{orderId}/wallet-payment", produces = [ApiVersion.VERSION_1_JSON])
    fun payOrderFromWallet(@AuthenticationPrincipal userId: UUID, @PathVariable orderId: UUID): Unit =
        commandBus(PayOrderFromWalletCommand(userId = userId, orderId = orderId))

    @PostMapping("/{orderId}/payment/{paymentMethod}", produces = [ApiVersion.VERSION_1_JSON])
    fun payOrder(
        @AuthenticationPrincipal userId: UUID,
        @PathVariable orderId: UUID,
        @PathVariable paymentMethod: TransactionPaymentMethod,
        @RequestParam clickId: String?,
    ): PayOrderCommand.Result = commandBus(
        PayOrderCommand(
            userId = userId,
            orderId = orderId,
            paymentMethod = paymentMethod,
            clickId = clickId
        )
    )

    @PostMapping("/{orderId}/payment", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun payForFreeOrder(
        @AuthenticationPrincipal userId: UUID,
        @PathVariable orderId: UUID,
        @RequestParam clickId: String?,
    ): Unit = commandBus(
        PayForFreeOrderCommand(
            userId = userId,
            orderId = orderId,
            clickId = clickId
        )
    )
}
