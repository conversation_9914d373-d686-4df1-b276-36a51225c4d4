package com.cleevio.fortraders.adapter.input.rest.dto

import com.cleevio.fortraders.application.module.discountcode.command.AdminPatchDiscountCodeCommand
import com.cleevio.fortraders.domain.model.discountcode.constant.DiscountCodeState
import com.cleevio.fortraders.domain.model.discountcode.constant.DiscountCodeType
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import java.math.BigDecimal
import java.time.LocalDate
import java.util.Optional
import java.util.UUID

data class AdminPatchDiscountCodeRequest(
    val type: DiscountCodeType?,
    val state: DiscountCodeState?,
    val description: String?,
    val percentage: Optional<Int>?,
    val amount: Optional<BigDecimal>?,
    val validFrom: LocalDate?,
    val validUntil: LocalDate?,
    val totalLimit: Optional<Int>?,
    val userLimit: Optional<Int>?,
    val challengePlanIds: Set<UUID>?,
    val userEmails: Set<String>?,
    val applicableToFirstUserOrderOnly: Boolean?,
    val platforms: Optional<Set<PlatformType>>?,
) {
    fun toCommand(discountCodeId: UUID) = AdminPatchDiscountCodeCommand(
        discountCodeId = discountCodeId,
        type = type,
        state = state,
        description = description,
        percentage = percentage,
        amount = amount,
        validFrom = validFrom,
        validUntil = validUntil,
        totalLimit = totalLimit,
        userLimit = userLimit,
        challengePlanIds = challengePlanIds,
        userEmails = userEmails,
        applicableToFirstUserOrderOnly = applicableToFirstUserOrderOnly,
        platforms = platforms
    )
}
