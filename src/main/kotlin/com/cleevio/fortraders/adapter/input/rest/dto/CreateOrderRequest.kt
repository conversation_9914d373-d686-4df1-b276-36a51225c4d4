package com.cleevio.fortraders.adapter.input.rest.dto

import com.cleevio.fortraders.application.module.order.command.CreateOrderCommand
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import java.util.UUID

data class CreateOrderRequest(
    val challengeId: UUID,
    val challengePlanId: UUID,
    val platform: PlatformType,
    val profitTargetSettingId: UUID?,
    val maxDrawdownSettingId: UUID,
    val dailyDrawdownSettingId: UUID,
    val payoutsTypeSettingId: UUID,
    val profitSplitSettingId: UUID,
    val refundSettingId: UUID,
    val dailyPauseSettingId: UUID,
    val dailyProfitCapSettingId: UUID,
    val discountCode: String?,
    val affiliateCode: String?,
) {

    fun toCommand(userId: UUID, countryIsoCode: String?) = CreateOrderCommand(
        userId = userId,
        challengeId = challengeId,
        challengePlanId = challengePlanId,
        platform = platform,
        profitTargetSettingId = profitTargetSettingId,
        maxDrawdownSettingId = maxDrawdownSettingId,
        dailyDrawdownSettingId = dailyDrawdownSettingId,
        payoutsTypeSettingId = payoutsTypeSettingId,
        profitSplitSettingId = profitSplitSettingId,
        refundSettingId = refundSettingId,
        dailyPauseSettingId = dailyPauseSettingId,
        dailyProfitCapSettingId = dailyProfitCapSettingId,
        discountCode = discountCode,
        affiliateCode = affiliateCode,
        countryIsoCode = countryIsoCode
    )
}
