package com.cleevio.fortraders.domain.model.challengestepsetting.exception

import com.cleevio.fortraders.infrastructure.exception.CustomErrorReasonTypes
import com.cleevio.fortraders.infrastructure.exception.ForTradersApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.BAD_REQUEST)
class ChallengeStepSettingTypeNotAllowedForGivenStepException : ForTradersApiException(
    reason = CustomErrorReasonTypes.CHALLENGE_STEP_SETTING_TYPE_NOT_ALLOWED,
    message = "ChallengeStepSetting type is not allowed for given step."
)
