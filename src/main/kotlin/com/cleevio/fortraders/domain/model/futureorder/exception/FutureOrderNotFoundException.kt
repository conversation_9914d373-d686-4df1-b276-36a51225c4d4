package com.cleevio.fortraders.domain.model.futureorder.exception

import com.cleevio.fortraders.infrastructure.exception.CustomErrorReasonTypes
import com.cleevio.fortraders.infrastructure.exception.ForTradersApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class FutureOrderNotFoundException : ForTradersApiException(
    reason = CustomErrorReasonTypes.FUTURE_ORDER_NOT_FOUND,
    message = "FutureOrder not found."
)
