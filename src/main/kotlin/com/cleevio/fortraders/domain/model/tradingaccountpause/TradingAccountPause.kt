package com.cleevio.fortraders.domain.model.tradingaccountpause

import com.cleevio.fortraders.domain.CreatableEntity
import com.cleevio.fortraders.domain.model.tradingaccountpause.constant.TradingAccountPauseType
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import java.math.BigDecimal
import java.util.UUID

@Entity
class TradingAccountPause(
    val tradingAccountId: UUID,

    @Enumerated(EnumType.STRING)
    val pauseType: TradingAccountPauseType,

    val accountBalance: BigDecimal,
    val accountEquity: BigDecimal,
) : CreatableEntity()
