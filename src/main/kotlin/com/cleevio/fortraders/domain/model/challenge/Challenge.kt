package com.cleevio.fortraders.domain.model.challenge

import com.cleevio.fortraders.domain.UpdatableEntity
import com.cleevio.fortraders.domain.model.challenge.constant.ChallengeState
import com.cleevio.fortraders.domain.model.challenge.constant.ChallengeType
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import java.math.BigDecimal

@Entity
class Challenge(
    name: String,
    startingBalance: BigDecimal,
    type: ChallengeType,
) : UpdatableEntity() {

    var name: String = name
        private set

    var startingBalance: BigDecimal = startingBalance
        private set

    @Enumerated(EnumType.STRING)
    var state: ChallengeState = ChallengeState.DISABLED
        private set

    @Enumerated(EnumType.STRING)
    var type: ChallengeType = type
        private set

    fun patchAdminSpecifiedProperties(name: String?, startingBalance: BigDecimal?, state: ChallengeState?, type: ChallengeType?) {
        name?.let { this.name = it }
        startingBalance?.let { this.startingBalance = it }
        state?.let { this.state = it }
        type?.let { this.type = it }
    }
}
