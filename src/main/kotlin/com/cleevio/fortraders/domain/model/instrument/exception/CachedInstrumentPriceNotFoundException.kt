package com.cleevio.fortraders.domain.model.instrument.exception

import com.cleevio.fortraders.infrastructure.exception.CustomErrorReasonTypes
import com.cleevio.fortraders.infrastructure.exception.ForTradersApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class CachedInstrumentPriceNotFoundException : ForTradersApiException(
    reason = CustomErrorReasonTypes.CACHED_INSTRUMENT_PRICE_NOT_FOUND,
    message = "Cached instrument price not found."
)
