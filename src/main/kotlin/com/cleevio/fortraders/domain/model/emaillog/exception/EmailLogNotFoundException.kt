package com.cleevio.fortraders.domain.model.emaillog.exception

import com.cleevio.fortraders.infrastructure.exception.CustomErrorReasonTypes
import com.cleevio.fortraders.infrastructure.exception.ForTradersApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class EmailLogNotFoundException : ForTradersApiException(
    reason = CustomErrorReasonTypes.EMAIL_LOG_NOT_FOUND,
    message = "Email log not found."
)
