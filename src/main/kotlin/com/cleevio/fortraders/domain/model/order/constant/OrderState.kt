package com.cleevio.fortraders.domain.model.order.constant

enum class OrderState {
    PENDING, // when created
    COMPLETED, // when paid successfully
    PENDING_CANCELLATION, // when subscription is cancelled manually
    RECURRING_PAYMENT_ATTEMPT_FAILED, // when subscription payment attempt failed
    CANCELLED, // when failed recurring payment was received
    CANCELLED_NOT_PAID, // when initial payment failed
    ;

    fun isCancellationState(): Boolean = this in CANCELLATION_STATES
}

private val CANCELLATION_STATES = setOf(
    OrderState.PENDING_CANCELLATION,
    OrderState.RECURRING_PAYMENT_ATTEMPT_FAILED,
    OrderState.CANCELLED,
    OrderState.CANCELLED_NOT_PAID
)
