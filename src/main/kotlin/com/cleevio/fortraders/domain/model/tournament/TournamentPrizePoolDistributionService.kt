package com.cleevio.fortraders.domain.model.tournament

import com.cleevio.fortraders.application.common.constant.TOURNAMENT
import com.cleevio.fortraders.application.common.util.percentageToDecimal
import com.cleevio.fortraders.application.module.tournament.TournamentFinderService
import com.cleevio.fortraders.application.module.tournament.constant.DISTRIBUTE_PRIZE_POOL
import com.cleevio.fortraders.application.module.tournamentuser.TournamentUserFinderService
import com.cleevio.fortraders.application.module.transaction.TransactionService
import com.cleevio.fortraders.domain.model.tournament.constant.TournamentReward
import com.cleevio.fortraders.infrastructure.config.logger
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockArgumentParameter
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.math.RoundingMode
import java.util.UUID

@Service
class TournamentPrizePoolDistributionService(
    private val tournamentFinderService: TournamentFinderService,
    private val tournamentUserFinderService: TournamentUserFinderService,
    private val transactionService: TransactionService,

    @Value("\${fortraders.tournament.prize-pool-distribution-max-places}")
    private val prizePoolDistributionMaxPlaces: Int,
) {
    private val logger = logger()

    fun calculatePrize(initialPrizePool: BigDecimal?): Map<Int, BigDecimal> = calculatePrize(
        initialPrizePool = initialPrizePool,
        positions = 1..prizePoolDistributionMaxPlaces
    )

    fun calculatePrize(initialPrizePool: BigDecimal?, positions: IntRange): Map<Int, BigDecimal> {
        initialPrizePool ?: return emptyMap()

        return positions
            .associateWith { prizePoolDistributions.getOrDefault(it, 0.0) }
            .mapValues {
                (initialPrizePool * it.value.percentageToDecimal()).setScale(0, RoundingMode.HALF_UP)
            }
    }

    @Transactional
    @Lock(module = TOURNAMENT, lockName = DISTRIBUTE_PRIZE_POOL)
    fun distributePrizePool(@LockArgumentParameter tournamentId: UUID) {
        val tournament = tournamentFinderService.getById(tournamentId).also {
            if (!it.hasEnded() || it.reward != TournamentReward.MONEY) return
        }

        val prizePoolDistributions = tournament.initialPrizePool?.let { calculatePrize(it) } ?: run {
            logger.info("Tournament $tournamentId has no prize pool, skipping prize pool distribution")
            return
        }
        tournamentUserFinderService.findAllByTournamentIdWithPositiveTotalProfitPercentageOrderedByTotalProfitPercentage(
            tournamentId = tournamentId,
            limit = prizePoolDistributionMaxPlaces
        ).forEachIndexed { index, tournamentUser ->
            val userPrize = prizePoolDistributions.getValue(index + 1)
            logger.info("Distributing prize $userPrize to user ${tournamentUser.userId} in tournament $tournamentId")
            transactionService.createTournamentPrizeDepositTransaction(
                userId = tournamentUser.userId,
                amount = userPrize
            )
        }
    }
}

private val prizePoolDistributions: Map<Int, Double> = mapOf(
    1 to 18.0,
    2 to 14.0,
    3 to 12.0,
    4 to 9.5,
    5 to 5.0,
    6 to 4.0,
    7 to 3.5,
    8 to 2.5,
    9 to 2.0,
    10 to 1.5,
    *((11..15).map { it to 1.3 }.toTypedArray()),
    *((16..20).map { it to 1.0 }.toTypedArray()),
    *((21..35).map { it to 0.7 }.toTypedArray()),
    *((36..50).map { it to 0.4 }.toTypedArray())
)

typealias PrizePoolDistribution = (initialPrizePool: BigDecimal?) -> Map<Int, BigDecimal>
