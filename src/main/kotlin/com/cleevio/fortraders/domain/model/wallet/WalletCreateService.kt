package com.cleevio.fortraders.domain.model.wallet

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class WalletCreateService(
    private val walletRepository: WalletRepository,
) {
    @Transactional
    fun create(userId: UUID): Wallet = walletRepository.save(
        Wallet(
            userId = userId
        )
    )
}
