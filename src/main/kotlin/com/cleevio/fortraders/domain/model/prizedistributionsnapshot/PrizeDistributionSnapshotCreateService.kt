package com.cleevio.fortraders.domain.model.prizedistributionsnapshot

import com.cleevio.fortraders.domain.model.prizedistribution.PrizeDistribution
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class PrizeDistributionSnapshotCreateService(
    private val prizeDistributionSnapshotRepository: PrizeDistributionSnapshotRepository,
) {

    @Transactional
    fun createAll(tournamentId: UUID, prizeDistributions: List<PrizeDistribution>) {
        prizeDistributions.map {
            PrizeDistributionSnapshot(
                tournamentId = tournamentId,
                players = it.players,
                position = it.position,
                challengeStartingBalance = it.challengeStartingBalance
            )
        }.let {
            prizeDistributionSnapshotRepository.saveAll(it)
        }
    }
}
