package com.cleevio.fortraders.domain.model.tournament.exception

import com.cleevio.fortraders.infrastructure.exception.CustomErrorReasonTypes
import com.cleevio.fortraders.infrastructure.exception.ForTradersApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.BAD_REQUEST)
class TournamentHasEndedException : ForTradersApiException(
    reason = CustomErrorReasonTypes.TOURNAMENT_HAS_ENDED,
    message = "Tournament has already ended."
)
