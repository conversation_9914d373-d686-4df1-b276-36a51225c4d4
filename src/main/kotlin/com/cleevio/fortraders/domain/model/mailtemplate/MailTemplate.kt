package com.cleevio.fortraders.domain.model.mailtemplate

import com.cleevio.fortraders.domain.UpdatableEntity
import com.cleevio.fortraders.domain.model.mailtemplate.constant.MailType
import com.cleevio.fortraders.domain.model.user.constant.PreferredLanguage
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated

@Entity
class MailTemplate(
    @Enumerated(EnumType.STRING)
    val type: MailType,

    @Enumerated(EnumType.STRING)
    val language: PreferredLanguage,
    subject: String,
    htmlBody: String,
    jsonBody: String,
) : UpdatableEntity() {
    var subject: String = subject
        private set

    var htmlBody: String = htmlBody
        private set

    var jsonBody: String = jsonBody
        private set

    fun adminUpdateProperties(subject: String, htmlBody: String, jsonBody: String) {
        this.subject = subject
        this.htmlBody = htmlBody
        this.jsonBody = jsonBody
    }
}
