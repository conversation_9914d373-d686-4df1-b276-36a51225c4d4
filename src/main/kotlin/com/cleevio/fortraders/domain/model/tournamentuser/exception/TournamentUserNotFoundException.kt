package com.cleevio.fortraders.domain.model.tournamentuser.exception

import com.cleevio.fortraders.infrastructure.exception.CustomErrorReasonTypes
import com.cleevio.fortraders.infrastructure.exception.ForTradersApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class TournamentUserNotFoundException : ForTradersApiException(
    reason = CustomErrorReasonTypes.TOURNAMENT_USER_NOT_FOUND,
    message = "TournamentUser with given ID not found."
)
