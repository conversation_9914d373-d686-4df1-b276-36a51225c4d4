package com.cleevio.fortraders.infrastructure.config

import com.cleevio.fortraders.application.module.scheduledaudit.ScheduledAuditService
import org.aspectj.lang.ProceedingJoinPoint
import org.aspectj.lang.annotation.Around
import org.aspectj.lang.annotation.Aspect
import org.aspectj.lang.reflect.MethodSignature
import org.springframework.core.Ordered
import org.springframework.core.annotation.Order
import org.springframework.stereotype.Component
import java.time.Duration
import java.time.Instant

@Aspect
@Component
@Order(Ordered.LOWEST_PRECEDENCE)
class ScheduledTaskAuditAspect(
    private val scheduledAuditService: ScheduledAuditService,
) {
    @Around("@annotation(org.springframework.scheduling.annotation.Scheduled)")
    fun auditScheduledTasks(joinPoint: ProceedingJoinPoint): Any? {
        val methodSignature = joinPoint.signature as MethodSignature
        val method = methodSignature.method
        val className = method.declaringClass.simpleName

        val startTime = Instant.now()
        return try {
            joinPoint.proceed()
        } finally {
            scheduledAuditService.recordExecution(
                name = className,
                lastInvocationTime = startTime,
                lastExecutionDuration = Duration.between(startTime, Instant.now())
            )
        }
    }
}
