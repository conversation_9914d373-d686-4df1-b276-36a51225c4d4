package com.cleevio.fortraders.infrastructure.config

import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Configuration
import org.springframework.http.CacheControl
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer
import java.util.concurrent.TimeUnit

@Configuration
class StaticResourcesConfig(
    @Value("\${fortraders.storage-path}")
    private val storagePath: String,
    @Value("\${fortraders.resources-path}")
    private val resourcesPath: String,
) : WebMvcConfigurer {
    override fun addResourceHandlers(registry: ResourceHandlerRegistry) {
        registry.addResourceHandler("$resourcesPath/**")
            .addResourceLocations("file:$storagePath/")
            .setCacheControl(CacheControl.maxAge(3600L * 24, TimeUnit.SECONDS).cachePublic())
    }
}
