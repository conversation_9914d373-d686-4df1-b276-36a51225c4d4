package com.cleevio.fortraders.infrastructure.security

import com.cleevio.fortraders.application.common.util.ifFalse
import com.cleevio.fortraders.infrastructure.config.logger
import com.stripe.net.Webhook
import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.security.web.util.matcher.AntPathRequestMatcher
import org.springframework.util.StreamUtils
import org.springframework.web.filter.OncePerRequestFilter

class StripeSignatureFilter(
    private val requestMatcher: AntPathRequestMatcher,
    private val secretKey: String,
) : OncePerRequestFilter() {
    private val log = logger()

    override fun shouldNotFilter(request: HttpServletRequest): Boolean {
        return !requestMatcher.matches(request)
    }

    override fun doFilterInternal(request: HttpServletRequest, response: HttpServletResponse, filterChain: FilterChain) {
        val signatureHeaderValue = request.getHeader("Stripe-Signature")
        val cachedRequest = CachedBodyHttpServletRequestWrapper(request)
        val requestBody = StreamUtils.copyToByteArray(cachedRequest.getInputStream())

        try {
            Webhook.Signature.verifyHeader(
                requestBody.toString(Charsets.UTF_8),
                signatureHeaderValue,
                secretKey,
                DEFAULT_TOLERANCE
            ).ifFalse {
                error("Stripe signature verification failed")
            }
        } catch (e: Exception) {
            log.error("Failed to verify Stripe signature", e)
            response.status = HttpServletResponse.SC_UNAUTHORIZED
            return
        }

        filterChain.doFilter(cachedRequest, response)
    }
}

private const val DEFAULT_TOLERANCE = 300L
