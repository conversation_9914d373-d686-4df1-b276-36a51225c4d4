package com.cleevio.fortraders.infrastructure.security

import jakarta.servlet.ReadListener
import jakarta.servlet.ServletInputStream
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletRequestWrapper
import org.springframework.util.StreamUtils
import java.io.BufferedReader
import java.io.IOException
import java.io.InputStream

class CachedBodyHttpServletRequestWrapper(
    request: HttpServletRequest,
) : HttpServletRequestWrapper(request) {
    private val cachedBody: ByteArray = StreamUtils.copyToByteArray(request.inputStream)

    override fun getInputStream(): ServletInputStream = CachedBodyServletInputStream(cachedBody)

    override fun getReader(): BufferedReader = cachedBody.inputStream().reader().buffered()
}

private class CachedBodyServletInputStream(
    cachedBody: ByteArray,
) : ServletInputStream() {
    private val cachedBodyInputStream: InputStream = cachedBody.inputStream()

    override fun read(): Int = cachedBodyInputStream.read()

    override fun isFinished(): Boolean = try {
        cachedBodyInputStream.available() == 0
    } catch (e: IOException) {
        false
    }

    override fun isReady(): Boolean = true

    override fun setReadListener(listener: ReadListener?) {
        throw UnsupportedOperationException()
    }

    override fun read(b: ByteArray, off: Int, len: Int): Int = cachedBodyInputStream.read(b, off, len)
}
