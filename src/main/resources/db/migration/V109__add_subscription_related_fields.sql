ALTER TABLE public."challenge_plan"
    DROP COLUMN "stocks_price",
    ADD COLUMN "is_subscription" BOOLEAN NOT NULL DEFAULT FALSE;

ALTER TABLE public."challenge_plan"
    ALTER COLUMN "is_subscription" DROP DEFAULT;

ALTER TABLE public."order"
    DROP COLUMN "buy_in_transaction_id",
    DROP COLUMN "deposit_transaction_id";

ALTER TABLE "transaction"
    ADD COLUMN "order_id" UUID,
    ADD COLUMN provider_subscription_id TEXT,
    ADD CONSTRAINT "78688845f8874ad2af3b_fk" FOREIGN KEY ("order_id") REFERENCES "order" ("id");

CREATE INDEX "88ea71c168b5405190ed_ui" ON "transaction" ("order_id");
