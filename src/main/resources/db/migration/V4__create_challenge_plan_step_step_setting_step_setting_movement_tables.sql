-- challenge_plan
CREATE TABLE "challenge_plan"
(
    id             UUID PRIMARY KEY,
    challenge_id   UUID        NOT NULL,
    state          TEXT        NOT NULL,
    title          TEXT        NOT NULL,
    steps          INTEGER     NOT NULL,
    base_price     NUMERIC     NOT NULL,
    stocks_enabled BOOLEAN     NOT NULL,
    stocks_price   NUMERIC,
    created_at     TIMESTAMPTZ NOT NULL,
    updated_at     TIMESTAMPTZ NOT NULL,
    created_by     UUID,
    updated_by     UUID,

    CONSTRAINT "2f7fadccf07e4376a48a_fk" FOREIGN KEY (challenge_id) REFERENCES "challenge" (id)
);
CREATE INDEX "43ed8ceb3b3e4eed8857_ix" ON "challenge_plan" (challenge_id);

-- challenge_step
CREATE TABLE "challenge_step"
(
    id                            UUID PRIMARY KEY,
    challenge_plan_id             UUID        NOT NULL,
    next_step_id                  UUID,
    number                        INTEGER     NOT NULL,
    type                          TEXT        NOT NULL,
    max_drawdown_type             TEXT        NOT NULL,
    leverage                      INTEGER     NOT NULL,
    profit_share                  INTEGER     NOT NULL,
    inactivity_period_days        INTEGER     NOT NULL,
    min_trading_days              INTEGER     NOT NULL,
    max_trading_days              INTEGER,
    initial_withdrawal_delay_days INTEGER     NOT NULL,
    liquidate_friday              BOOLEAN     NOT NULL,
    group_mt4                     TEXT,
    group_mt4_stocks              TEXT,
    group_mt5                     TEXT,
    group_mt5_stocks              TEXT,
    created_at                    TIMESTAMPTZ NOT NULL,
    updated_at                    TIMESTAMPTZ NOT NULL,
    created_by                    UUID,
    updated_by                    UUID,

    CONSTRAINT "af692c964f294bd89ff9_fk" FOREIGN KEY (challenge_plan_id) REFERENCES "challenge_plan" (id),
    CONSTRAINT "1acf572316584d9dbd80_fk" FOREIGN KEY (next_step_id) REFERENCES "challenge_step" (id)
);
CREATE INDEX "7eed869f35e4409a8208_ix" ON "challenge_step" (challenge_plan_id);
CREATE UNIQUE INDEX "f7bafea9e33e43dead57_ui" ON "challenge_step" (next_step_id);

-- challenge_step_setting
CREATE TABLE "challenge_step_setting"
(
    id                UUID PRIMARY KEY,
    challenge_step_id UUID        NOT NULL,
    type              TEXT        NOT NULL,
    created_at        TIMESTAMPTZ NOT NULL,
    updated_at        TIMESTAMPTZ NOT NULL,
    created_by        UUID,
    updated_by        UUID,

    CONSTRAINT "1e668312c6fe4ac4aeed_fk" FOREIGN KEY (challenge_step_id) REFERENCES "challenge_step" (id)
);
CREATE INDEX "ae56cabf15624bf09c94_ix" ON "challenge_step_setting" (challenge_step_id);

-- challenge_step_setting_movement
CREATE TABLE "challenge_step_setting_movement"
(
    id                        UUID PRIMARY KEY,
    challenge_step_setting_id UUID        NOT NULL,
    type                      TEXT        NOT NULL,
    percentage_value          INTEGER,
    list_value                TEXT,
    movement_percentage_value INTEGER,
    movement_absolute_value   NUMERIC,
    is_default                BOOLEAN     NOT NULL,
    created_at                TIMESTAMPTZ NOT NULL,
    updated_at                TIMESTAMPTZ NOT NULL,
    deleted_at                TIMESTAMPTZ,
    created_by                UUID,
    updated_by                UUID,

    CONSTRAINT "63aa866b78b24f7599cd_fk" FOREIGN KEY (challenge_step_setting_id) REFERENCES "challenge_step_setting" (id)
);
CREATE INDEX "56336f08bf334b8591fd_ix" ON "challenge_step_setting_movement" (challenge_step_setting_id) WHERE deleted_at IS NULL;
