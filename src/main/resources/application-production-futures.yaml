sentry:
  environment: fortraders-futures-production

spring:
  application:
    name: fortraders-futures
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://securetoken.google.com/fortraders-futures-production
  mail:
    host: smtp.eu.mailgun.org
    port: 587

cleevio:
  distributed-locks:
    redis:
      database: 21

springdoc:
  api-docs:
    enabled: false

integration:
  proxy:
    host: *********
    port: 8888
  instrument-proxy:
    base-ws-url: ws://fortraders-proxy-production-cluster-ip-service.fortraders-proxy-production.svc.cluster.local:8080/ws
  trade-locker:
    base-url: https://api.tradelocker.com
    environment: DEMO
  dx-trade:
    base-url: https://fortradersx.com
  dx-feed:
    base-url: https://services.get.dxfeed.com
  c-trader:
    manager:
      access-token: ""
      login: 0
      password: ""
  slack:
    orders-channel-id: "C080LLY8GHM"
    funded-account-channel-id: "C082F2DPBSR"
    payouts-channel-id: "C082B8PCK2S"
    affiliate-payouts-channel-id: "C082B8PCK2S"
    account-breach-channel-id: "C082B9320P8"
  my-fatoorah:
    base-url: "https://api.myfatoorah.com/"
  wordpress:
    base-url: https://futures-wp-production.fortraders.cleevio.dev/

fortraders:
  deployment:
    prop-firm: "FOR_TRADERS_X"
  mail:
    from-address: "Futures For Traders <<EMAIL>>"
  api:
    base-url: https://api.futuresfortraders.com
  web-app:
    base-url: https://app.futuresfortraders.com/
  admin-app:
    base-url: https://admin.futuresfortraders.com/
  storage:
    local-url: https://cdn.futuresfortraders.com
    google-cloud-storage:
      bucket-name: fortraders-futures-production.firebasestorage.app
  transaction:
    subscription-interval: MONTHLY
  trading-account:
    platform:
      servers:
        META_TRADER_5: FTTrading-Server
        TRADE_LOCKER: FTLOCK
      urls:
        META_TRADER_5: https://mt5.fortraders.com/terminal
        TRADE_LOCKER: https://demo.tradelocker.com/
        DX_TRADE: https://fortradersx.com/
        LEGACY: https://fortraders.com
      login-range:
        DX_TRADE:
          from: 1_000_000
          to: 9_999_999
  invoice:
    billing-details-html: >-
      <span class="bold">BLN Analytics Club DMCC</span><br/>
      Goldcrest Executive Building<br/>
      Office No. 1210, JLT<br/>
      Cluster C, Dubai<br/>
      United Arab Emirates
    support-email: "<EMAIL>"
  trade:
    synchronisation:
      cron: "-"
  # TODO remove after fully tested
  notification:
    unfinished-order:
      cron: "-"
    inactive-breached-accounts:
      cron: "-"
    top-user-ranking:
      cron: "-"
