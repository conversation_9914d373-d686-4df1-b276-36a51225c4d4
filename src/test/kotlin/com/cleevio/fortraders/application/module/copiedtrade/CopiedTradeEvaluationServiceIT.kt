package com.cleevio.fortraders.application.module.copiedtrade

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.module.copiedtrade.scheduled.CopiedTradeEvaluationTrigger
import com.cleevio.fortraders.domain.model.copiedtrade.CopiedTradeRepository
import com.cleevio.fortraders.domain.model.scheduledaudit.ScheduledAudit
import com.cleevio.fortraders.domain.model.scheduledaudit.ScheduledAuditRepository
import com.cleevio.fortraders.domain.model.systemSetting.constant.SystemSettingType
import com.cleevio.fortraders.domain.model.trade.constant.TradeSide
import com.cleevio.fortraders.domain.model.trade.constant.TradeState
import com.cleevio.fortraders.domain.model.trade.constant.TradeType
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit

class CopiedTradeEvaluationServiceIT(
    private val copiedTradeRepository: CopiedTradeRepository,
    private val scheduledAuditRepository: ScheduledAuditRepository,
    private val underTest: CopiedTradeEvaluationService,
) : IntegrationTest({

    "should create copied trade when two accounts have similar trades" {
        // Setup system settings for copied trade evaluation
        testDataHelper.getSystemSetting(
            type = SystemSettingType.COPY_TRADES_MAX_OPEN_TIME_DELTA_SECONDS,
            value = "60"
        )
        testDataHelper.getSystemSetting(
            type = SystemSettingType.COPY_TRADES_MAX_CLOSE_TIME_DELTA_SECONDS,
            value = "60"
        )
        testDataHelper.getSystemSetting(
            type = SystemSettingType.COPY_TRADES_MAX_VOLUME_DIFF,
            value = "5"
        )
        testDataHelper.getSystemSetting(
            type = SystemSettingType.COPY_TRADES_MAX_OPEN_PRICE_DIFF_PERCENTAGE,
            value = "5"
        )
        testDataHelper.getSystemSetting(
            type = SystemSettingType.COPY_TRADES_MAX_CLOSE_PRICE_DIFF_PERCENTAGE,
            value = "5"
        )

        // Create scheduled audit for CopiedTradeEvaluationTrigger
        scheduledAuditRepository.save(
            ScheduledAudit(
                name = CopiedTradeEvaluationTrigger::class.qualifiedName!!,
                lastInvocationTime = Instant.now().minus(1, ChronoUnit.DAYS),
                lastExecutionDuration = java.time.Duration.ZERO
            )
        )

        // Create two users
        val user1 = testDataHelper.getUser(index = 1)
        val user2 = testDataHelper.getUser(index = 2)

        // Create challenge and challenge plan
        val challenge = testDataHelper.getChallenge()
        val challengePlan = testDataHelper.getChallengePlan(challengeId = challenge.id)
        val challengeStep = testDataHelper.getChallengeStep(challengePlanId = challengePlan.id)

        // Create orders for both users
        val order1 = testDataHelper.getOrder(userId = user1.id, challengeId = challenge.id, challengePlanId = challengePlan.id)
        val order2 = testDataHelper.getOrder(userId = user2.id, challengeId = challenge.id, challengePlanId = challengePlan.id)

        // Create trading accounts for both users
        val tradingAccount1 = testDataHelper.getTradingAccount(
            accountId = "account1",
            orderId = order1.id,
            challengeStepId = challengeStep.id
        )
        val tradingAccount2 = testDataHelper.getTradingAccount(
            accountId = "account2",
            orderId = order2.id,
            challengeStepId = challengeStep.id
        )

        // Create source trade (opened earlier)
        val openTime1 = Instant.now().minus(30, ChronoUnit.MINUTES)
        val closeTime1 = Instant.now().minus(15, ChronoUnit.MINUTES)
        val sourceTrade = testDataHelper.getTrade(
            tradingAccountId = tradingAccount1.id,
            dealId = "source-deal-1",
            positionId = "source-position-1",
            type = TradeType.FOREX,
            side = TradeSide.BUY,
            state = TradeState.CLOSED,
            volume = BigDecimal("0.1"),
            symbol = "EURUSD",
            openTime = openTime1,
            openPrice = BigDecimal("1.10"),
            closeTime = closeTime1,
            closePrice = BigDecimal("1.15"),
            profit = BigDecimal("500")
        )

        // Create copier trade (opened later)
        val openTime2 = openTime1.plus(10, ChronoUnit.SECONDS)
        val closeTime2 = closeTime1.plus(10, ChronoUnit.SECONDS)
        val copierTrade = testDataHelper.getTrade(
            tradingAccountId = tradingAccount2.id,
            dealId = "copier-deal-1",
            positionId = "copier-position-1",
            type = TradeType.FOREX,
            side = TradeSide.BUY,
            state = TradeState.CLOSED,
            volume = BigDecimal("0.1"),
            symbol = "EURUSD",
            openTime = openTime2,
            openPrice = BigDecimal("1.11"),
            closeTime = closeTime2,
            closePrice = BigDecimal("1.16"),
            profit = BigDecimal("500")
        )

        // Execute the service method
        underTest.evaluateCopiedTrades()

        // Verify that a copied trade was created
        val copiedTrades = copiedTradeRepository.findAll()
        copiedTrades shouldHaveSize 1

        val copiedTrade = copiedTrades[0]
        copiedTrade.copierTradingAccountId shouldBe tradingAccount2.id
        copiedTrade.sourceTradingAccountId shouldBe tradingAccount1.id
        copiedTrade.copierTradeId shouldBe copierTrade.id
        copiedTrade.sourceTradeId shouldBe sourceTrade.id
    }
})
