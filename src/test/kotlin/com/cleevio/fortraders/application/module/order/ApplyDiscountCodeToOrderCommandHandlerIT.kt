package com.cleevio.fortraders.application.module.order

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.module.order.command.ApplyDiscountCodeToOrderCommand
import com.cleevio.fortraders.domain.model.discountcode.constant.DiscountCodeState
import com.cleevio.fortraders.domain.model.discountcode.constant.DiscountCodeType
import com.cleevio.fortraders.domain.model.discountcode.exception.DiscountCodeAlreadyUsedException
import com.cleevio.fortraders.domain.model.discountcode.exception.DiscountCodeCountryLimitReachedException
import com.cleevio.fortraders.domain.model.discountcode.exception.DiscountCodeNotApplicableException
import com.cleevio.fortraders.domain.model.discountcode.exception.DiscountCodeNotFoundException
import com.cleevio.fortraders.domain.model.order.OrderRepository
import com.cleevio.fortraders.domain.model.order.constant.OrderDiscountType
import com.cleevio.fortraders.domain.model.order.constant.OrderState
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import java.time.Instant
import java.time.temporal.ChronoUnit

class ApplyDiscountCodeToOrderCommandHandlerIT(
    private val orderRepository: OrderRepository,
) : IntegrationTest({

    "should throw because enabled code not found" {
        val order = testDataHelper.getOrder()
        testDataHelper.getDiscountCode(code = "MAY", state = DiscountCodeState.DISABLED)

        shouldThrowExactly<DiscountCodeNotFoundException> {
            commandBus(
                ApplyDiscountCodeToOrderCommand(
                    code = "MAY",
                    orderId = order.id,
                    userId = order.userId,
                    countryIsoCode = null
                )
            )
        }
    }

    "should throw because discount code is not allowed for given challenge plan" {
        val challenge = testDataHelper.getChallenge()
        val challengePlan1 = testDataHelper.getChallengePlan(challengeId = challenge.id)
        val challengePlan2 = testDataHelper.getChallengePlan(challengeId = challenge.id)
        val order = testDataHelper.getOrder(challengeId = challengePlan1.challengeId, challengePlanId = challengePlan1.id)
        val discountCode1 = testDataHelper.getDiscountCode(code = "MAY", state = DiscountCodeState.ENABLED)
        val discountCode2 = testDataHelper.getDiscountCode(code = "JUNE", state = DiscountCodeState.ENABLED)
        testDataHelper.getDiscountCodeChallengePlan(discountCodeId = discountCode1.id, challengePlanId = challengePlan2.id)

        shouldThrowExactly<DiscountCodeNotApplicableException> {
            commandBus(
                ApplyDiscountCodeToOrderCommand(
                    code = "MAY",
                    orderId = order.id,
                    userId = order.userId,
                    countryIsoCode = null
                )
            )
        }
    }

    "should throw because discount code is not allowed for given user" {
        val user1 = testDataHelper.getUser(index = 1)
        val user2 = testDataHelper.getUser(index = 2)
        val challenge = testDataHelper.getChallenge()
        val challengePlan1 = testDataHelper.getChallengePlan(challengeId = challenge.id)
        val challengePlan2 = testDataHelper.getChallengePlan(challengeId = challenge.id)
        val order = testDataHelper.getOrder(
            userId = user1.id,
            challengeId = challengePlan1.challengeId,
            challengePlanId = challengePlan1.id
        )
        val discountCode1 = testDataHelper.getDiscountCode(code = "MAY", state = DiscountCodeState.ENABLED)
        val discountCode2 = testDataHelper.getDiscountCode(code = "JUNE", state = DiscountCodeState.ENABLED)
        testDataHelper.getDiscountCodeUser(discountCodeId = discountCode1.id, userId = user2.id)

        shouldThrowExactly<DiscountCodeNotApplicableException> {
            commandBus(
                ApplyDiscountCodeToOrderCommand(
                    code = "MAY",
                    orderId = order.id,
                    userId = user1.id,
                    countryIsoCode = null
                )
            )
        }
    }

    "should throw because user already has existing order" {
        val user1 = testDataHelper.getUser(index = 1)
        val user2 = testDataHelper.getUser(index = 2)
        val challenge = testDataHelper.getChallenge()
        val challengePlan1 = testDataHelper.getChallengePlan(challengeId = challenge.id)
        val discountCode1 = testDataHelper.getDiscountCode(
            code = "MAY",
            state = DiscountCodeState.ENABLED,
            userLimit = null,
            totalLimit = null,
            applicableToFirstUserOrderOnly = true
        )
        testDataHelper.getOrder(
            userId = user1.id,
            challengeId = challengePlan1.challengeId,
            challengePlanId = challengePlan1.id,
            discountCodeId = null,
            paidAt = Instant.now()
        )
        testDataHelper.getOrder(
            userId = user2.id,
            challengeId = challengePlan1.challengeId,
            challengePlanId = challengePlan1.id,
            discountCodeId = discountCode1.id
        )
        val order = testDataHelper.getOrder(
            userId = user1.id,
            challengeId = challengePlan1.challengeId,
            challengePlanId = challengePlan1.id,
            discountCodeId = null
        )
        shouldThrowExactly<DiscountCodeAlreadyUsedException> {
            commandBus(
                ApplyDiscountCodeToOrderCommand(
                    code = "MAY",
                    orderId = order.id,
                    userId = user1.id,
                    countryIsoCode = null
                )
            )
        }
    }

    "should throw because user has reached the limit of discount code usages" {
        val user1 = testDataHelper.getUser(index = 1)
        val user2 = testDataHelper.getUser(index = 2)
        val challenge = testDataHelper.getChallenge()
        val challengePlan1 = testDataHelper.getChallengePlan(challengeId = challenge.id)
        val challengePlan2 = testDataHelper.getChallengePlan(challengeId = challenge.id)
        val discountCode1 = testDataHelper.getDiscountCode(
            code = "MAY",
            state = DiscountCodeState.ENABLED,
            userLimit = 2,
            totalLimit = null,
            applicableToFirstUserOrderOnly = false
        )
        val discountCode2 = testDataHelper.getDiscountCode(
            code = "JUNE",
            state = DiscountCodeState.ENABLED,
            userLimit = 0,
            totalLimit = null
        )
        testDataHelper.getOrder(
            userId = user1.id,
            challengeId = challengePlan1.challengeId,
            challengePlanId = challengePlan1.id,
            discountCodeId = discountCode1.id,
            paidAt = Instant.parse("2021-01-01T00:00:00Z")
        )
        testDataHelper.getOrder(
            userId = user1.id,
            challengeId = challengePlan2.challengeId,
            challengePlanId = challengePlan2.id,
            discountCodeId = discountCode1.id,
            paidAt = Instant.parse("2021-01-01T00:00:00Z")
        )
        testDataHelper.getOrder(
            userId = user2.id,
            challengeId = challengePlan1.challengeId,
            challengePlanId = challengePlan1.id,
            discountCodeId = discountCode1.id,
            paidAt = Instant.parse("2021-01-01T00:00:00Z")
        )
        val order = testDataHelper.getOrder(
            userId = user1.id,
            challengeId = challengePlan1.challengeId,
            challengePlanId = challengePlan1.id,
            discountCodeId = null
        )
        shouldThrowExactly<DiscountCodeNotApplicableException> {
            commandBus(
                ApplyDiscountCodeToOrderCommand(
                    code = "MAY",
                    orderId = order.id,
                    userId = user1.id,
                    countryIsoCode = null
                )
            )
        }
    }

    "should throw because total limit of discount code usages has been reached" {
        val user1 = testDataHelper.getUser(index = 1)
        val user2 = testDataHelper.getUser(index = 2)
        val challenge = testDataHelper.getChallenge()
        val challengePlan1 = testDataHelper.getChallengePlan(challengeId = challenge.id)
        val challengePlan2 = testDataHelper.getChallengePlan(challengeId = challenge.id)
        val discountCode1 = testDataHelper.getDiscountCode(
            code = "MAY",
            state = DiscountCodeState.ENABLED,
            userLimit = null,
            totalLimit = 2
        )
        val discountCode2 = testDataHelper.getDiscountCode(
            code = "JUNE",
            state = DiscountCodeState.ENABLED,
            userLimit = null,
            totalLimit = null
        )
        testDataHelper.getOrder(
            userId = user1.id,
            challengeId = challengePlan1.challengeId,
            challengePlanId = challengePlan1.id,
            discountCodeId = discountCode1.id,
            paidAt = Instant.now()
        )
        testDataHelper.getOrder(
            userId = user1.id,
            challengeId = challengePlan2.challengeId,
            challengePlanId = challengePlan2.id,
            discountCodeId = discountCode1.id,
            paidAt = Instant.now()
        )
        testDataHelper.getOrder(
            userId = user2.id,
            challengeId = challengePlan1.challengeId,
            challengePlanId = challengePlan1.id,
            discountCodeId = discountCode2.id
        )
        val order = testDataHelper.getOrder(
            userId = user1.id,
            challengeId = challengePlan1.challengeId,
            challengePlanId = challengePlan1.id,
            discountCodeId = null
        )
        shouldThrowExactly<DiscountCodeNotApplicableException> {
            commandBus(
                ApplyDiscountCodeToOrderCommand(
                    code = "MAY",
                    orderId = order.id,
                    userId = user1.id,
                    countryIsoCode = null
                )
            )
        }
    }

    "should throw because discount code is not allowed for given platform" {
        val challenge = testDataHelper.getChallenge()
        val challengePlan1 = testDataHelper.getChallengePlan(challengeId = challenge.id)
        val order = testDataHelper.getOrder(
            challengeId = challengePlan1.challengeId,
            challengePlanId = challengePlan1.id,
            platform = PlatformType.TRADE_LOCKER
        )
        val discountCode1 = testDataHelper.getDiscountCode(
            code = "MAY",
            state = DiscountCodeState.ENABLED,
            platforms = setOf(PlatformType.C_TRADER)
        )
        val discountCode2 = testDataHelper.getDiscountCode(code = "JUNE", state = DiscountCodeState.ENABLED)

        shouldThrowExactly<DiscountCodeNotApplicableException> {
            commandBus(
                ApplyDiscountCodeToOrderCommand(
                    code = "MAY",
                    orderId = order.id,
                    userId = order.userId,
                    countryIsoCode = null
                )
            )
        }
    }

    "should apply discount code to order" {
        val user1 = testDataHelper.getUser(index = 1)
        val user2 = testDataHelper.getUser(index = 2)
        val challenge = testDataHelper.getChallenge()
        val challengePlan1 = testDataHelper.getChallengePlan(challengeId = challenge.id)
        val challengePlan2 = testDataHelper.getChallengePlan(challengeId = challenge.id)
        val discountCode1 = testDataHelper.getDiscountCode(
            code = "MAY",
            state = DiscountCodeState.ENABLED,
            type = DiscountCodeType.FIXED,
            amount = 20L.toBigDecimal(),
            percentage = null,
            platforms = setOf(PlatformType.TRADE_LOCKER)
        )
        val discountCode2 = testDataHelper.getDiscountCode(
            code = "JUNE",
            state = DiscountCodeState.ENABLED,
            type = DiscountCodeType.FIXED,
            amount = 1000L.toBigDecimal(),
            percentage = null
        )
        val order1 = testDataHelper.getOrder(
            userId = user2.id,
            challengeId = challengePlan1.challengeId,
            challengePlanId = challengePlan1.id,
            discountCodeId = null,
            price = 9999.9.toBigDecimal()
        )
        val order2 = testDataHelper.getOrder(
            userId = user1.id,
            challengeId = challengePlan1.challengeId,
            challengePlanId = challengePlan1.id,
            discountCodeId = null,
            price = 1000L.toBigDecimal(),
            platform = PlatformType.TRADE_LOCKER
        )

        val result = commandBus(
            ApplyDiscountCodeToOrderCommand(
                code = "MAY",
                orderId = order2.id,
                userId = user1.id,
                countryIsoCode = null
            )
        )
        result.orderId shouldBe order2.id
        result.startingPrice shouldBeEqualComparingTo 1000L.toBigDecimal()
        result.discountType shouldBe OrderDiscountType.DISCOUNT_CODE
        result.discountedPrice shouldBeEqualComparingTo 980L.toBigDecimal()
        result.code shouldBe "MAY"
        result.discountAmount shouldBeEqualComparingTo 20L.toBigDecimal()
        result.discountPercentage.shouldBeNull()

        val orders = orderRepository.findAll().sortedBy { it.createdAt }
        orders shouldHaveSize 2
        orders[0].let {
            it.id shouldBe order1.id
            it.discountCodeId.shouldBeNull()
            it.startingPrice shouldBeEqualComparingTo 9999.9.toBigDecimal()
            it.price shouldBeEqualComparingTo 9999.9.toBigDecimal()
        }
        orders[1].let {
            it.id shouldBe order2.id
            it.discountCodeId shouldBe discountCode1.id
            it.startingPrice shouldBeEqualComparingTo 1000L.toBigDecimal()
            it.price shouldBeEqualComparingTo 980L.toBigDecimal()
        }
    }

    "should apply discount code to order with country code" {
        val user1 = testDataHelper.getUser(index = 1)
        val user2 = testDataHelper.getUser(index = 2)
        val country1 = testDataHelper.getCountry(name = "Slovakia", isoCode = "SK")
        val country2 = testDataHelper.getCountry(name = "Czechia", isoCode = "CZ")
        testDataHelper.getContact(userId = user1.id, countryId = country1.id)
        testDataHelper.getContact(userId = user2.id, countryId = country2.id)
        val challenge = testDataHelper.getChallenge()
        val challengePlan = testDataHelper.getChallengePlan(challengeId = challenge.id)
        val discountCode = testDataHelper.getDiscountCode(
            code = "COUNTRY",
            state = DiscountCodeState.ENABLED,
            type = DiscountCodeType.FIXED,
            amount = 30L.toBigDecimal(),
            percentage = null,
            perCountryLimit = 1
        )
        // order without discount code
        testDataHelper.getOrder(
            userId = user1.id,
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id,
            discountCodeId = null,
            price = 1000L.toBigDecimal()
        ) {
            it.markAsCompleted()
        }
        // order from yesterday with discount code
        testDataHelper.getOrder(
            userId = user1.id,
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id,
            discountCodeId = discountCode.id,
            price = 1000L.toBigDecimal(),
            state = OrderState.COMPLETED,
            paidAt = Instant.now().minus(1, ChronoUnit.DAYS)
        )
        // other user order
        testDataHelper.getOrder(
            userId = user2.id,
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id,
            discountCodeId = discountCode.id,
            price = 1000L.toBigDecimal()
        ) {
            it.markAsCompleted()
        }
        val order = testDataHelper.getOrder(
            userId = user1.id,
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id,
            discountCodeId = null,
            price = 1000L.toBigDecimal()
        )

        val result = commandBus(
            ApplyDiscountCodeToOrderCommand(
                code = "COUNTRY",
                orderId = order.id,
                userId = user1.id,
                countryIsoCode = "SK"
            )
        )

        result.orderId shouldBe order.id
        result.startingPrice shouldBeEqualComparingTo 1000L.toBigDecimal()
        result.discountType shouldBe OrderDiscountType.DISCOUNT_CODE
        result.discountedPrice shouldBeEqualComparingTo 970L.toBigDecimal()
        result.code shouldBe "COUNTRY"
        result.discountAmount shouldBeEqualComparingTo 30L.toBigDecimal()
        result.discountPercentage.shouldBeNull()

        val orders = orderRepository.findAll().filter { it.id == order.id }
        orders shouldHaveSize 1
        orders[0].let {
            it.id shouldBe order.id
            it.discountCodeId shouldBe discountCode.id
            it.startingPrice shouldBeEqualComparingTo 1000L.toBigDecimal()
            it.price shouldBeEqualComparingTo 970L.toBigDecimal()
        }
    }

    "should throw because country limit for discount code has been reached" {
        val user1 = testDataHelper.getUser(index = 1)
        val user2 = testDataHelper.getUser(index = 2)
        val country1 = testDataHelper.getCountry(name = "Slovakia", isoCode = "SK")
        val country2 = testDataHelper.getCountry(name = "Czechia", isoCode = "CZ")
        testDataHelper.getContact(userId = user1.id, countryId = country1.id)
        testDataHelper.getContact(userId = user2.id, countryId = country2.id)
        val challenge = testDataHelper.getChallenge()
        val challengePlan = testDataHelper.getChallengePlan(challengeId = challenge.id)
        val discountCode = testDataHelper.getDiscountCode(
            code = "COUNTRY",
            state = DiscountCodeState.ENABLED,
            type = DiscountCodeType.FIXED,
            amount = 30L.toBigDecimal(),
            percentage = null,
            perCountryLimit = 1
        )
        // order without discount code
        testDataHelper.getOrder(
            userId = user1.id,
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id,
            discountCodeId = null,
            price = 1000L.toBigDecimal()
        ) {
            it.markAsCompleted()
        }
        // order with discount code
        testDataHelper.getOrder(
            userId = user1.id,
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id,
            discountCodeId = discountCode.id,
            price = 1000L.toBigDecimal(),
            state = OrderState.COMPLETED,
            paidAt = Instant.now()
        )
        // other user order
        testDataHelper.getOrder(
            userId = user2.id,
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id,
            discountCodeId = discountCode.id,
            price = 1000L.toBigDecimal()
        ) {
            it.markAsCompleted()
        }
        val order = testDataHelper.getOrder(
            userId = user1.id,
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id,
            discountCodeId = null,
            price = 1000L.toBigDecimal()
        )

        shouldThrowExactly<DiscountCodeCountryLimitReachedException> {
            commandBus(
                ApplyDiscountCodeToOrderCommand(
                    code = "COUNTRY",
                    orderId = order.id,
                    userId = user1.id,
                    countryIsoCode = "SK"
                )
            )
        }
    }
})
