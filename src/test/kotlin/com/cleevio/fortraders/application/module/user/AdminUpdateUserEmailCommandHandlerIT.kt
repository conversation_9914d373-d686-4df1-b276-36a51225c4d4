package com.cleevio.fortraders.application.module.user

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.application.module.user.command.AdminUpdateUserEmailCommand
import com.cleevio.fortraders.application.module.user.port.output.UpdateFirebaseUserEmail
import com.cleevio.fortraders.domain.model.user.UserRepository
import com.cleevio.fortraders.domain.model.user.exception.UserEmailAlreadyExistsException
import com.cleevio.fortraders.domain.model.user.exception.UserFirebaseUpdateFailedException
import com.cleevio.fortraders.domain.model.user.exception.UserNotFoundException
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.verify
import org.springframework.beans.factory.annotation.Autowired

class AdminUpdateUserEmailCommandHandlerIT(
    private val userRepository: UserRepository,
    @Autowired private val updateFirebaseUserEmail: UpdateFirebaseUserEmail,
) : IntegrationTest({

    "should correctly update user's email" {
        every { updateFirebaseUserEmail(any(), any()) } returns Result.success(Unit)

        val user = testDataHelper.getUser(
            firebaseId = "123",
            email = "<EMAIL>"
        )

        commandBus(
            AdminUpdateUserEmailCommand(
                userId = user.id,
                email = "<EMAIL>"
            )
        )

        val users = userRepository.findAll()
        users shouldHaveSize 1
        users[0].let {
            it.email shouldBe "<EMAIL>"
        }

        verify {
            updateFirebaseUserEmail(
                firebaseId = "123",
                email = "<EMAIL>"
            )
        }
    }

    "should not update email if it's the same as current email" {
        val user = testDataHelper.getUser(
            email = "<EMAIL>"
        )

        val command = AdminUpdateUserEmailCommand(
            userId = user.id,
            email = "<EMAIL>"
        )

        commandBus(command)

        val users = userRepository.findAll()
        users shouldHaveSize 1
        users[0].let {
            it.email shouldBe "<EMAIL>"
        }

        verify(exactly = 0) {
            updateFirebaseUserEmail(any(), any())
        }
    }

    "should throw if user does not exist" {
        shouldThrowExactly<UserNotFoundException> {
            commandBus(
                AdminUpdateUserEmailCommand(
                    userId = "63afc0a4-a6bd-4d6c-97e6-c091271dc786".toUUID(),
                    email = "<EMAIL>"
                )
            )
        }
    }

    "should throw if email already exists" {
        val user1 = testDataHelper.getUser(
            firebaseId = "123",
            email = "<EMAIL>"
        )
        val user2 = testDataHelper.getUser(
            firebaseId = "456",
            email = "<EMAIL>"
        )

        shouldThrowExactly<UserEmailAlreadyExistsException> {
            commandBus(
                AdminUpdateUserEmailCommand(
                    userId = user1.id,
                    email = "<EMAIL>"
                )
            )
        }
    }

    "should throw if firebase update fails" {
        every { updateFirebaseUserEmail(any(), any()) } returns Result.failure(RuntimeException("Firebase update failed"))

        val user = testDataHelper.getUser(
            firebaseId = "123",
            email = "<EMAIL>"
        )

        shouldThrowExactly<UserFirebaseUpdateFailedException> {
            commandBus(
                AdminUpdateUserEmailCommand(
                    userId = user.id,
                    email = "<EMAIL>"
                )
            )
        }

        val users = userRepository.findAll()
        users shouldHaveSize 1
        users[0].let {
            it.email shouldBe "<EMAIL>"
        }

        verify {
            updateFirebaseUserEmail(
                firebaseId = "123",
                email = "<EMAIL>"
            )
        }
    }
})
