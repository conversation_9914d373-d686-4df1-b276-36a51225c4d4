package com.cleevio.fortraders.application.module.discountcode

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.application.module.discountcode.command.AdminPatchDiscountCodeCommand
import com.cleevio.fortraders.domain.model.challengeplan.exception.ChallengePlanNotFoundException
import com.cleevio.fortraders.domain.model.discountcode.DiscountCodeRepository
import com.cleevio.fortraders.domain.model.discountcode.constant.DiscountCodeState
import com.cleevio.fortraders.domain.model.discountcode.constant.DiscountCodeType
import com.cleevio.fortraders.domain.model.discountcode.constant.DiscountUsageType
import com.cleevio.fortraders.domain.model.discountcode.exception.DiscountCodeNotFoundException
import com.cleevio.fortraders.domain.model.discountcodechallengeplan.DiscountCodeChallengePlanRepository
import com.cleevio.fortraders.domain.model.discountcodeuser.DiscountCodeUserRepository
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import com.cleevio.fortraders.toOptional
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import java.time.LocalDate

class AdminPatchDiscountCodeCommandHandlerIT(
    private val discountCodeRepository: DiscountCodeRepository,
    private val discountCodeUserRepository: DiscountCodeUserRepository,
    private val discountCodeChallengePlanRepository: DiscountCodeChallengePlanRepository,
) : IntegrationTest({

    "should throw because discount code does not exist" {
        testDataHelper.getDiscountCode(code = "code")
        discountCodeRepository.count() shouldBe 1

        shouldThrowExactly<DiscountCodeNotFoundException> {
            commandBus(
                AdminPatchDiscountCodeCommand(
                    discountCodeId = "71aaeae0-32b7-4b1b-8aec-f1c5c988b0de".toUUID(),
                    type = DiscountCodeType.PERCENTAGE,
                    state = DiscountCodeState.DISABLED,
                    description = "Sample description",
                    percentage = 10.toOptional(),
                    amount = null,
                    validFrom = LocalDate.parse("2021-01-01"),
                    validUntil = LocalDate.parse("2021-12-31"),
                    totalLimit = 100.toOptional(),
                    userLimit = 1.toOptional(),
                    challengePlanIds = setOf(),
                    userEmails = setOf(),
                    applicableToFirstUserOrderOnly = false,
                    platforms = null
                )
            )
        }

        discountCodeRepository.count() shouldBe 1
    }

    "should throw because given challenge plan does not exist" {
        val challengePlan = testDataHelper.getChallengePlan()
        val discountCode = testDataHelper.getDiscountCode(code = "code")
        discountCodeRepository.count() shouldBe 1

        shouldThrowExactly<ChallengePlanNotFoundException> {
            commandBus(
                AdminPatchDiscountCodeCommand(
                    discountCodeId = discountCode.id,
                    type = DiscountCodeType.PERCENTAGE,
                    state = DiscountCodeState.DISABLED,
                    description = "Sample description",
                    percentage = 10.toOptional(),
                    amount = null,
                    validFrom = LocalDate.parse("2021-01-01"),
                    validUntil = LocalDate.parse("2021-12-31"),
                    totalLimit = 100.toOptional(),
                    userLimit = 1.toOptional(),
                    challengePlanIds = setOf(
                        challengePlan.id,
                        "71aaeae0-32b7-4b1b-8aec-f1c5c988b0de".toUUID()
                    ),
                    userEmails = setOf(),
                    applicableToFirstUserOrderOnly = false,
                    platforms = null
                )
            )
        }
    }

    "should patch discount code and re-create associated entities" {
        val user1 = testDataHelper.getUser(index = 1)
        val user2 = testDataHelper.getUser(index = 2)
        val challengePlan1 = testDataHelper.getChallengePlan(index = 1)
        val challengePlan2 = testDataHelper.getChallengePlan(index = 2)
        val discountCode1 = testDataHelper.getDiscountCode(
            code = "code1",
            type = DiscountCodeType.FIXED,
            usageType = DiscountUsageType.GLOBAL,
            state = DiscountCodeState.ENABLED,
            description = "Discount code description 1",
            percentage = null,
            amount = 9.99.toBigDecimal(),
            validFrom = LocalDate.parse("2019-01-01"),
            validUntil = LocalDate.parse("2019-01-31"),
            totalLimit = null,
            userLimit = null,
            platforms = null
        )
        val discountCode2 = testDataHelper.getDiscountCode(
            code = "code2",
            type = DiscountCodeType.PERCENTAGE,
            usageType = DiscountUsageType.GLOBAL,
            state = DiscountCodeState.ENABLED,
            description = "Discount code description 2",
            percentage = 10,
            amount = null,
            validFrom = LocalDate.parse("2018-01-01"),
            validUntil = LocalDate.parse("2018-01-31"),
            totalLimit = 1,
            userLimit = 2,
            platforms = setOf(PlatformType.DX_TRADE)
        )
        testDataHelper.getDiscountCodeChallengePlan(discountCodeId = discountCode1.id, challengePlanId = challengePlan1.id)
        testDataHelper.getDiscountCodeChallengePlan(discountCodeId = discountCode1.id, challengePlanId = challengePlan2.id)
        testDataHelper.getDiscountCodeChallengePlan(discountCodeId = discountCode2.id, challengePlanId = challengePlan1.id)
        testDataHelper.getDiscountCodeUser(discountCodeId = discountCode1.id, userId = user1.id)
        testDataHelper.getDiscountCodeUser(discountCodeId = discountCode1.id, userId = user2.id)
        testDataHelper.getDiscountCodeUser(discountCodeId = discountCode2.id, userId = user1.id)

        commandBus(
            AdminPatchDiscountCodeCommand(
                discountCodeId = discountCode1.id,
                type = DiscountCodeType.PERCENTAGE,
                state = DiscountCodeState.DISABLED,
                description = "Sample description",
                percentage = 10.toOptional(),
                amount = null,
                validFrom = LocalDate.parse("2021-01-01"),
                validUntil = LocalDate.parse("2021-12-31"),
                totalLimit = 100.toOptional(),
                userLimit = 1.toOptional(),
                challengePlanIds = setOf(
                    challengePlan2.id
                ),
                userEmails = setOf(
                    user2.email
                ),
                applicableToFirstUserOrderOnly = true,
                platforms = setOf(PlatformType.TRADE_LOCKER).toOptional()
            )
        )

        val discountCodes = discountCodeRepository.findAll().sortedBy { it.createdAt }
        discountCodes shouldHaveSize 2
        discountCodes[0].let {
            it.id shouldBe discountCode1.id
            it.state shouldBe DiscountCodeState.DISABLED
            it.code shouldBe "CODE1"
            it.type shouldBe DiscountCodeType.PERCENTAGE
            it.usageType shouldBe DiscountUsageType.GLOBAL
            it.description shouldBe "Sample description"
            it.percentage shouldBe 10
            it.amount.shouldBeNull()
            it.validFrom shouldBe LocalDate.parse("2021-01-01")
            it.validUntil shouldBe LocalDate.parse("2021-12-31")
            it.totalLimit shouldBe 100
            it.userLimit shouldBe 1
            it.applicableToFirstUserOrderOnly shouldBe true
            it.platforms shouldBe setOf(PlatformType.TRADE_LOCKER)
        }
        discountCodes[1].let {
            it.id shouldBe discountCode2.id
            it.state shouldBe DiscountCodeState.ENABLED
            it.code shouldBe "CODE2"
            it.type shouldBe DiscountCodeType.PERCENTAGE
            it.usageType shouldBe DiscountUsageType.GLOBAL
            it.description shouldBe "Discount code description 2"
            it.percentage shouldBe 10
            it.amount.shouldBeNull()
            it.validFrom shouldBe LocalDate.parse("2018-01-01")
            it.validUntil shouldBe LocalDate.parse("2018-01-31")
            it.totalLimit shouldBe 1
            it.userLimit shouldBe 2
            it.applicableToFirstUserOrderOnly shouldBe false
            it.platforms shouldBe setOf(PlatformType.DX_TRADE)
        }

        val discountCodeUsers = discountCodeUserRepository.findAll().sortedBy { it.createdAt }
        discountCodeUsers shouldHaveSize 2
        discountCodeUsers[0].let {
            it.discountCodeId shouldBe discountCode2.id
            it.userId shouldBe user1.id
        }
        discountCodeUsers[1].let {
            it.discountCodeId shouldBe discountCode1.id
            it.userId shouldBe user2.id
        }

        val discountCodeChallengePlans = discountCodeChallengePlanRepository.findAll().sortedBy { it.createdAt }
        discountCodeChallengePlans shouldHaveSize 2
        discountCodeChallengePlans[0].let {
            it.discountCodeId shouldBe discountCode2.id
            it.challengePlanId shouldBe challengePlan1.id
        }
        discountCodeChallengePlans[1].let {
            it.discountCodeId shouldBe discountCode1.id
            it.challengePlanId shouldBe challengePlan2.id
        }
    }
})
