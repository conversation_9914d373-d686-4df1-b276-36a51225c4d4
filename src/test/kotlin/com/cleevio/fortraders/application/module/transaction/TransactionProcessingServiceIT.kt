package com.cleevio.fortraders.application.module.transaction

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.domain.model.breach.constant.BreachType
import com.cleevio.fortraders.domain.model.transaction.TransactionRepository
import com.cleevio.fortraders.domain.model.transaction.constant.PaymentGatewayProvider
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionStatus
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionType
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.mockk.every
import io.mockk.verify
import java.time.Duration

class TransactionProcessingServiceIT(
    private val transactionRepository: TransactionRepository,
    private val underTest: TransactionProcessingService,
) : IntegrationTest({

    "should do nothing if breach is not final and not last evaluation step" {
        every { cancelSubscriptionMock(any()) } returns Result.success(Unit)

        val user = testDataHelper.getUser()
        val wallet = testDataHelper.getWallet(userId = user.id)
        val challengePlan = testDataHelper.getChallengePlan(steps = 3)
        val challengeStep = testDataHelper.getChallengeStep(challengePlanId = challengePlan.id, number = 1)
        val order = testDataHelper.getOrder(
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id,
            userId = user.id
        )
        testDataHelper.getTransaction(
            walletId = wallet.id,
            orderId = order.id,
            amount = 1000.5.toBigDecimal(),
            type = TransactionType.RECURRING_DEPOSIT
        ) {
            it.changeStatus(TransactionStatus.COMPLETED)
            it.registerProviderSubscription(
                provider = PaymentGatewayProvider.MY_FATOORAH,
                providerSubscriptionId = "123",
                subscriptionInterval = Duration.ofDays(1)
            )
        }
        val tradingAccount = testDataHelper.getTradingAccount(
            challengeStepId = challengeStep.id,
            accountId = "105",
            orderId = order.id,
            isLastEvaluationStep = false
        )
        testDataHelper.getBreach(
            tradingAccountId = tradingAccount.id,
            type = BreachType.PROFIT_TARGET
        )

        underTest.processTransactionAfterBreach(
            tradingAccountId = tradingAccount.id,
            breachType = BreachType.PROFIT_TARGET
        )

        val transactions = transactionRepository.findAll()
        transactions shouldHaveSize 1
        transactions[0].nextPaymentAt.shouldNotBeNull()

        verify(exactly = 0) {
            cancelSubscriptionMock(any())
        }
    }

    "should cancel subscription if breach is final " {
        every { cancelSubscriptionMock(any()) } returns Result.success(Unit)

        val user = testDataHelper.getUser()
        val wallet = testDataHelper.getWallet(userId = user.id)
        val challengePlan = testDataHelper.getChallengePlan(steps = 3)
        val challengeStep = testDataHelper.getChallengeStep(challengePlanId = challengePlan.id, number = 1)
        val order = testDataHelper.getOrder(
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id,
            userId = user.id
        )
        testDataHelper.getTransaction(
            walletId = wallet.id,
            orderId = order.id,
            amount = 1000.5.toBigDecimal(),
            type = TransactionType.RECURRING_DEPOSIT
        ) {
            it.changeStatus(TransactionStatus.COMPLETED)
            it.registerProviderSubscription(
                provider = PaymentGatewayProvider.MY_FATOORAH,
                providerSubscriptionId = "123",
                subscriptionInterval = Duration.ofDays(1)
            )
        }
        val tradingAccount = testDataHelper.getTradingAccount(
            challengeStepId = challengeStep.id,
            accountId = "105",
            orderId = order.id,
            isLastEvaluationStep = false
        )
        testDataHelper.getBreach(
            tradingAccountId = tradingAccount.id,
            type = BreachType.MAX_DRAWDOWN
        )

        underTest.processTransactionAfterBreach(
            tradingAccountId = tradingAccount.id,
            breachType = BreachType.MAX_DRAWDOWN
        )

        val transactions = transactionRepository.findAll()
        transactions shouldHaveSize 1
        transactions[0].nextPaymentAt.shouldBeNull()

        verify {
            cancelSubscriptionMock("123")
        }
    }

    "should cancel subscription if breach is NOT final but is last evaluation step" {
        every { cancelSubscriptionMock(any()) } returns Result.success(Unit)

        val user = testDataHelper.getUser()
        val wallet = testDataHelper.getWallet(userId = user.id)
        val challengePlan = testDataHelper.getChallengePlan(steps = 3)
        val challengeStep = testDataHelper.getChallengeStep(challengePlanId = challengePlan.id, number = 1)
        val order = testDataHelper.getOrder(
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id,
            userId = user.id
        )
        testDataHelper.getTransaction(
            walletId = wallet.id,
            orderId = order.id,
            amount = 1000.5.toBigDecimal(),
            type = TransactionType.RECURRING_DEPOSIT
        ) {
            it.changeStatus(TransactionStatus.COMPLETED)
            it.registerProviderSubscription(
                provider = PaymentGatewayProvider.MY_FATOORAH,
                providerSubscriptionId = "123",
                subscriptionInterval = Duration.ofDays(1)
            )
        }
        val tradingAccount = testDataHelper.getTradingAccount(
            challengeStepId = challengeStep.id,
            accountId = "105",
            orderId = order.id,
            isLastEvaluationStep = true
        )
        testDataHelper.getBreach(
            tradingAccountId = tradingAccount.id,
            type = BreachType.PROFIT_TARGET
        )

        underTest.processTransactionAfterBreach(
            tradingAccountId = tradingAccount.id,
            breachType = BreachType.PROFIT_TARGET
        )

        val transactions = transactionRepository.findAll()
        transactions shouldHaveSize 1
        transactions[0].nextPaymentAt.shouldBeNull()

        verify {
            cancelSubscriptionMock("123")
        }
    }
})
