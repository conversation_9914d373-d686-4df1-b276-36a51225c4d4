package com.cleevio.fortraders.application.module.tournamenttrade.event.listener

import com.cleevio.fortraders.UnitTest
import com.cleevio.fortraders.application.module.tournamenttrade.TournamentTradeCachingService
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify

class ApplicationTournamentTradeEventListenerTest : UnitTest({

    val tournamentTradeCachingService = mockk<TournamentTradeCachingService>()
    val underTest = ApplicationTournamentTradeEventListener(
        tournamentTradeCachingService = tournamentTradeCachingService
    )

    afterAny {
        confirmVerified(tournamentTradeCachingService)
    }

    "should correctly call service method" {
        every { tournamentTradeCachingService.cacheOpenTournamentTrades() } just runs

        underTest.handleApplicationReadyEvent()

        verify {
            tournamentTradeCachingService.cacheOpenTournamentTrades()
        }
    }
})
