package com.cleevio.fortraders.application.module.transaction

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.module.transaction.command.CreateWalletDepositTransactionCommand
import com.cleevio.fortraders.application.module.transaction.port.output.CreatePaymentIntentResult
import com.cleevio.fortraders.domain.model.transaction.TransactionRepository
import com.cleevio.fortraders.domain.model.transaction.constant.DEFAULT_CURRENCY
import com.cleevio.fortraders.domain.model.transaction.constant.PaymentGatewayProvider
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionPaymentMethod
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionStatus
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionType
import com.cleevio.fortraders.domain.model.transaction.exception.TransactionCreationFailedException
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.verify

class CreateWalletDepositTransactionCommandHandlerIT(
    private val transactionRepository: TransactionRepository,
) : IntegrationTest({

    "should throw because payment intent could not be created" {
        every {
            createPaymentIntentMock(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
            )
        } returns Result.failure(
            Exception("TrustPay integration error")
        )
        val user = testDataHelper.getUser(email = "<EMAIL>", firstName = "John", lastName = "Doe")
        val wallet = testDataHelper.getWallet(userId = user.id)
        val contact = testDataHelper.getContact(
            userId = user.id,
            streetAddress = "Mojzisova",
            city = "Streda nad Bodrogom",
            postCode = "99123",
            phonePrefix = "+421",
            phoneNumber = "987654321"
        )

        shouldThrowExactly<TransactionCreationFailedException> {
            commandBus(
                CreateWalletDepositTransactionCommand(
                    userId = wallet.userId,
                    amount = 105.551.toBigDecimal(),
                    paymentMethod = TransactionPaymentMethod.CREDIT_CARD
                )
            )
        }

        transactionRepository.count() shouldBe 0

        verify {
            createPaymentIntentMock(
                transactionId = withArg { it.shouldNotBeNull() },
                amount = 105.56.toBigDecimal(),
                isSubscription = false,
                currency = DEFAULT_CURRENCY,
                city = "Streda nad Bodrogom",
                countryIsoCode = "SK",
                postCode = "99123",
                street = "Mojzisova",
                email = "<EMAIL>",
                firstName = "John",
                lastName = "Doe",
                fullPhoneNumber = "+421987654321"
            )
        }
    }

    "should correctly create and register transaction" {
        every {
            createPaymentIntentMock(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
            )
        } returns Result.success(
            CreatePaymentIntentResult(
                paymentId = "trust-pay-payment-id",
                paymentGatewayProvider = PaymentGatewayProvider.TRUST_PAY,
                paymentSecret = "123456"
            )
        )
        val user = testDataHelper.getUser(email = "<EMAIL>", firstName = "John", lastName = "Doe")
        val wallet = testDataHelper.getWallet(userId = user.id)
        val contact = testDataHelper.getContact(
            userId = user.id,
            streetAddress = "Mojzisova",
            city = "Streda nad Bodrogom",
            postCode = "99123",
            phonePrefix = "+421",
            phoneNumber = "987654321"
        )

        val result = commandBus(
            CreateWalletDepositTransactionCommand(
                userId = wallet.userId,
                amount = 105.551.toBigDecimal(),
                paymentMethod = TransactionPaymentMethod.CREDIT_CARD
            )
        )

        result.paymentGatewayProvider shouldBe PaymentGatewayProvider.TRUST_PAY
        result.providerPaymentId shouldBe "trust-pay-payment-id"
        result.providerPaymentSecret shouldBe "123456"

        val transactions = transactionRepository.findAll()
        transactions shouldHaveSize 1
        transactions[0].let {
            it.type shouldBe TransactionType.DEPOSIT
            it.amount shouldBeEqualComparingTo 105.56.toBigDecimal()
            it.status shouldBe TransactionStatus.REQUESTED
            it.provider shouldBe PaymentGatewayProvider.TRUST_PAY
            it.providerPaymentId shouldBe "trust-pay-payment-id"

            result.depositTransactionId shouldBe it.id
        }

        verify {
            createPaymentIntentMock(
                transactionId = transactions[0].id,
                amount = 105.56.toBigDecimal(),
                isSubscription = false,
                currency = DEFAULT_CURRENCY,
                city = "Streda nad Bodrogom",
                countryIsoCode = "SK",
                postCode = "99123",
                street = "Mojzisova",
                email = "<EMAIL>",
                firstName = "John",
                lastName = "Doe",
                fullPhoneNumber = "+421987654321"
            )
        }
    }
})
