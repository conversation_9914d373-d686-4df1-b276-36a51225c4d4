package com.cleevio.fortraders.application.module.emaillog

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.application.module.emaillog.command.AdminResendEmailLogCommand
import com.cleevio.fortraders.domain.model.emaillog.EmailLogRepository
import com.cleevio.fortraders.domain.model.emaillog.constant.EmailLogDeliveryStatus
import com.cleevio.fortraders.domain.model.emaillog.exception.EmailLogNotFoundException
import com.cleevio.fortraders.domain.model.mailtemplate.constant.MailType
import com.cleevio.fortraders.domain.model.mailtemplate.constant.MailVariables
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.matchers.shouldBe
import io.mockk.verify

class AdminResendEmailLogCommandHandlerIT(
    private val emailLogRepository: EmailLogRepository,
) : IntegrationTest({

    "should throw if email log does not exist" {
        shouldThrowExactly<EmailLogNotFoundException> {
            commandBus(
                AdminResendEmailLogCommand(
                    emailLogId = "6cfe1389-ee41-41ae-bd19-71ba54e2341c".toUUID()
                )
            )
        }
    }

    "should correctly resend email log" {
        val user = testDataHelper.getUser(firstName = "John", lastName = "Doe", email = "<EMAIL>")

        val mailVariables = mapOf(
            MailVariables.USER_FIRST_NAME to "John",
            MailVariables.USER_LAST_NAME to "Doe"
        )

        val emailLog = testDataHelper.getEmailLog(
            userId = user.id,
            recipientEmail = "<EMAIL>",
            mailType = MailType.PAYMENT_SUCCESS,
            subject = "Test Subject [[\${USER_LAST_NAME}]]",
            fromAddress = "<EMAIL>",
            htmlContent = "<p>Test content [[\${USER_FIRST_NAME}]]</p>",
            sentSuccessful = true,
            errorMessage = null,
            mailVariables = mailVariables
        )
        emailLogRepository.findAll().count() shouldBe 1

        commandBus(
            AdminResendEmailLogCommand(
                emailLogId = emailLog.id
            )
        )

        val emailLogs = emailLogRepository.findAll()
        emailLogs.count() shouldBe 2
        emailLogs.sortedByDescending { it.createdAt }[0].let {
            it.userId shouldBe user.id
            it.recipientEmail shouldBe "<EMAIL>"
            it.mailType shouldBe MailType.PAYMENT_SUCCESS
            it.subject shouldBe "Test Subject [[\${USER_LAST_NAME}]]"
            it.fromAddress shouldBe "Test FT<<EMAIL>>"
            it.htmlContent shouldBe "<p>Test content [[\${USER_FIRST_NAME}]]</p>"
            it.status shouldBe EmailLogDeliveryStatus.SENT
            it.errorMessage shouldBe null
        }

        verify(exactly = 1) {
            sendEmailNotificationMock(
                email = emailLog.recipientEmail,
                type = emailLog.mailType,
                subject = emailLog.subject,
                htmlBody = emailLog.htmlContent,
                mailVariables = emailLog.mailVariables
            )
        }
    }
})
