package com.cleevio.fortraders.application.module.order

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.application.module.order.query.GetUserOrderDetailQuery
import com.cleevio.fortraders.domain.model.breach.constant.BreachType
import com.cleevio.fortraders.domain.model.certificate.constant.CertificateType
import com.cleevio.fortraders.domain.model.challenge.constant.ChallengeType
import com.cleevio.fortraders.domain.model.challengestep.constant.ChallengeStepMaxDrawdownType
import com.cleevio.fortraders.domain.model.challengestep.constant.ChallengeStepType
import com.cleevio.fortraders.domain.model.discountcode.constant.DiscountCodeState
import com.cleevio.fortraders.domain.model.discountcode.constant.DiscountCodeType
import com.cleevio.fortraders.domain.model.discountcode.constant.DiscountUsageType
import com.cleevio.fortraders.domain.model.file.constant.FileType
import com.cleevio.fortraders.domain.model.futureorder.constant.FutureTradeSide
import com.cleevio.fortraders.domain.model.futureorder.constant.FutureTradeType
import com.cleevio.fortraders.domain.model.order.constant.OrderState
import com.cleevio.fortraders.domain.model.order.constant.PayoutsType
import com.cleevio.fortraders.domain.model.order.exception.OrderNotFoundException
import com.cleevio.fortraders.domain.model.trade.constant.TradeState
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import com.cleevio.fortraders.domain.model.tradingaccount.constant.TradingAccountState
import com.cleevio.fortraders.domain.model.usercontract.constant.UserContractState
import com.cleevio.fortraders.setAndReturnPrivateProperty
import com.cleevio.fortraders.shouldNotBeNullAndBeEqualComparingTo
import com.cleevio.fortraders.truncatedShouldBe
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.extensions.time.withConstantNow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate

class GetUserOrderDetailQueryHandlerIT : IntegrationTest({

    "should throw because order does not exist" {
        shouldThrowExactly<OrderNotFoundException> {
            queryBus(
                GetUserOrderDetailQuery(
                    userId = testDataHelper.getUser().id,
                    orderId = "1331cd99-3b30-45a5-8c27-43a56ab929b2".toUUID()
                )
            )
        }
    }

    "should correctly return result with user order" {
        val fakeTimeNow = Instant.parse("2023-01-01T12:59:00Z")
        val fakeDateNow = LocalDate.parse("2023-01-01")
        val userId1 = testDataHelper.getUser(index = 1, email = "<EMAIL>") {
            it.setAndReturnPrivateProperty("feedOnboardingUrl", "www.feed-onboarding-url.com")
        }.id
        val userId2 = testDataHelper.getUser(index = 2, email = "<EMAIL>").id

        val challenge1 = testDataHelper.getChallenge(
            index = 1,
            name = "challenge 1",
            startingBalance = 1111.toBigDecimal(),
            type = ChallengeType.CRYPTO
        )
        val challenge2 = testDataHelper.getChallenge(
            index = 2,
            name = "challenge 2",
            startingBalance = 2222.toBigDecimal(),
            type = ChallengeType.FOREX
        )
        val challenge4 = testDataHelper.getChallenge(
            index = 4,
            name = "challenge 4",
            startingBalance = 4444.toBigDecimal(),
            type = ChallengeType.FUTURES
        )

        val challengePlan1 = testDataHelper.getChallengePlan(
            index = 1,
            challengeId = challenge1.id,
            title = "challenge plan 1",
            steps = 3
        )
        val challengePlan2 = testDataHelper.getChallengePlan(
            index = 2,
            challengeId = challenge2.id,
            title = "challenge plan 2",
            steps = 2
        )
        val challengePlan4 = testDataHelper.getChallengePlan(
            index = 4,
            challengeId = challenge4.id,
            title = "challenge plan 4",
            steps = 1
        )

        val challengeStep11 = testDataHelper.getChallengeStep(
            challengePlanId = challengePlan1.id,
            number = 1,
            type = ChallengeStepType.EVALUATION
        )
        val challengeStep12 = testDataHelper.getChallengeStep(
            challengePlanId = challengePlan1.id,
            number = 2,
            type = ChallengeStepType.EVALUATION
        )
        val challengeStep13 = testDataHelper.getChallengeStep(
            challengePlanId = challengePlan1.id,
            number = 3,
            type = ChallengeStepType.FUNDED
        )
        val challengeStep21 = testDataHelper.getChallengeStep(
            challengePlanId = challengePlan2.id,
            number = 1,
            type = ChallengeStepType.EVALUATION
        )
        val challengeStep41 = testDataHelper.getChallengeStep(
            challengePlanId = challengePlan4.id,
            number = 1,
            type = ChallengeStepType.EVALUATION
        )
        val challengeStepSettings11 = prepareChallengeStepSettings(challengeStep11)
        val challengeStepSettings12 = prepareChallengeStepSettings(challengeStep12)
        val challengeStepSettings13 = prepareChallengeStepSettings(challengeStep13)
        prepareDefaultChallengeStepSettingMovements(
            challengeStepSettings = challengeStepSettings11,
            profitTarget = 1,
            maxDrawdown = 11,
            dailyDrawdown = 21,
            profitSplit = 31,
            refund = 41,
            dailyPause = 51,
            dailyProfitCap = 61,
            payoutsType = PayoutsType.BI_WEEKLY
        )
        prepareDefaultChallengeStepSettingMovements(
            challengeStepSettings = challengeStepSettings12,
            profitTarget = 2,
            maxDrawdown = 12,
            dailyDrawdown = 22,
            profitSplit = 32,
            refund = 42,
            dailyPause = 52,
            dailyProfitCap = 62,
            payoutsType = PayoutsType.WEEKLY
        )
        prepareDefaultChallengeStepSettingMovements(
            challengeStepSettings = challengeStepSettings13,
            profitTarget = 3,
            maxDrawdown = 13,
            dailyDrawdown = 23,
            profitSplit = 33,
            refund = 43,
            dailyPause = 53,
            dailyProfitCap = 63,
            payoutsType = PayoutsType.BI_WEEKLY
        )

        val discountCode1 = testDataHelper.getDiscountCode(
            code = "DISCOUNT_CODE_1",
            type = DiscountCodeType.PERCENTAGE,
            usageType = DiscountUsageType.BREACH,
            percentage = 10,
            amount = null,
            state = DiscountCodeState.ENABLED
        )
        testDataHelper.getDiscountCode(
            code = "DISCOUNT_CODE_2",
            type = DiscountCodeType.FIXED,
            usageType = DiscountUsageType.BREACH,
            percentage = null,
            amount = 22.5.toBigDecimal(),
            state = DiscountCodeState.ENABLED
        )

        val order1 = testDataHelper.getOrder(
            userId = userId1,
            challengeId = challenge1.id,
            challengePlanId = challengePlan1.id,
            platform = PlatformType.META_TRADER_5,
            state = OrderState.CANCELLED,
            startingBalance = 111.1.toBigDecimal()
        ) {
            it.setBreachDiscountCode(discountCode1.id)
        }
        val order2 = testDataHelper.getOrder(
            userId = userId1,
            challengeId = challenge2.id,
            challengePlanId = challengePlan2.id,
            startingBalance = 222.2.toBigDecimal()
        )
        val order4 = testDataHelper.getOrder(
            userId = userId2,
            challengeId = challenge4.id,
            challengePlanId = challengePlan4.id,
            startingBalance = 444.4.toBigDecimal()
        )
        prepareOrderChallengeStepSettingMovements(
            orderId = order1.id,
            challengeStepSettings = challengeStepSettings11,
            profitTarget = 4,
            maxDrawdown = 14,
            dailyDrawdown = null,
            profitSplit = 34,
            refund = 44,
            dailyPause = 54,
            dailyProfitCap = 64,
            payoutsType = PayoutsType.WEEKLY
        )

        val tradingAccount11 = testDataHelper.getTradingAccount(
            accountId = "105",
            state = TradingAccountState.BREACHED,
            orderId = order1.id,
            platform = PlatformType.META_TRADER_5,
            challengeStepId = challengeStep11.id,
            password = "password11",
            tradingGroup = "trading group 11",
            startingBalance = 123.4.toBigDecimal(),
            currentBalance = 456.7.toBigDecimal(),
            equity = 500.5.toBigDecimal(),
            profitTarget = 25,
            maxDrawdown = 20,
            dailyDrawdown = 10,
            minProfitableTradingDays = 5,
            maxTradingDays = 55,
            leverage = 15,
            dailyPause = 33,
            dailyProfitCap = 43,
            challengeStepMaxDrawdownType = ChallengeStepMaxDrawdownType.STANDARD,
            inactivityPeriodDays = 329,
            isLastEvaluationStep = false,
            capTrailingDrawdownAfterPayout = true,
            startingBalanceThisPayoutCycle = 100.0.toBigDecimal()
        )
        val tradingAccount12 = testDataHelper.getTradingAccount(
            accountId = "106",
            state = TradingAccountState.ACTIVE,
            orderId = order1.id,
            platform = PlatformType.TRADE_LOCKER,
            challengeStepId = challengeStep12.id,
            password = "password12",
            tradingGroup = "trading group 12",
            startingBalance = 987.6.toBigDecimal(),
            currentBalance = 543.2.toBigDecimal(),
            equity = 600.6.toBigDecimal(),
            profitTarget = 35,
            maxDrawdown = 30,
            dailyDrawdown = 20,
            minProfitableTradingDays = 15,
            maxTradingDays = 65,
            leverage = 25,
            consistencyTarget = 50,
            challengeStepMaxDrawdownType = ChallengeStepMaxDrawdownType.TRAILING,
            isLastEvaluationStep = true,
            capTrailingDrawdownAfterPayout = false,
            startingBalanceThisPayoutCycle = 500.0.toBigDecimal()
        )
        val tradingAccount21 = testDataHelper.getTradingAccount(
            accountId = "108",
            state = TradingAccountState.ACTIVE,
            orderId = order2.id,
            challengeStepId = challengeStep21.id
        )
        val tradingAccount41 = testDataHelper.getTradingAccount(
            accountId = "110",
            state = TradingAccountState.ACTIVE,
            orderId = order4.id,
            challengeStepId = challengeStep41.id
        )

        testDataHelper.getBreach(
            tradingAccountId = tradingAccount11.id,
            type = BreachType.PROFIT_TARGET,
            breachedAt = Instant.parse("2023-01-02T12:59:00Z"),
            accountEquity = 600.5.toBigDecimal()
        )

        val tradingAccountHistory1 = testDataHelper.getTradingAccountHistory(
            tradingAccountId = tradingAccount11.id,
            startingBalance = 0.001.toBigDecimal(),
            balance = 1.111.toBigDecimal(),
            equity = 2.222.toBigDecimal(),
            balanceMaxDrawdown = 3.333.toBigDecimal(),
            balanceDailyDrawdown = 4.444.toBigDecimal(),
            date = fakeDateNow,
            balanceProfitTarget = 5.555.toBigDecimal()
        )
        testDataHelper.getTradingAccountHistory(
            tradingAccountId = tradingAccount12.id,
            balance = 999.99.toBigDecimal(),
            equity = 199.99.toBigDecimal(),
            date = LocalDate.parse("2021-01-01"),
            profitableTradingDays = 4,
            balanceMaxDrawdown = 299.99.toBigDecimal(),
            dailyProfit = 15.5.toBigDecimal(),
            balanceDailyDrawdown = null
        )
        testDataHelper.getTradingAccountHistory(
            tradingAccountId = tradingAccount21.id,
            balance = 9999999L.toBigDecimal(),
            equity = 9999999L.toBigDecimal(),
            date = LocalDate.parse("2021-01-01")
        )

        testDataHelper.getTrade(
            tradingAccountId = tradingAccount11.id,
            dealId = "1",
            positionId = "1",
            state = TradeState.OPEN,
            openTime = fakeTimeNow,
            closeTime = Instant.EPOCH
        )
        testDataHelper.getTrade(
            tradingAccountId = tradingAccount11.id,
            dealId = "11",
            positionId = "11",
            state = TradeState.CLOSED,
            openTime = Instant.parse("2021-02-01T00:01:00Z"),
            closeTime = Instant.parse("2021-02-01T10:00:00Z")
        )
        testDataHelper.getTrade(
            tradingAccountId = tradingAccount12.id,
            dealId = "2",
            positionId = "2",
            state = TradeState.OPEN,
            openTime = Instant.parse("2021-02-01T00:00:00Z"),
            closeTime = Instant.EPOCH
        )
        testDataHelper.getTrade(
            tradingAccountId = tradingAccount12.id,
            dealId = "3",
            positionId = "3",
            state = TradeState.OPEN,
            openTime = Instant.parse("2021-12-24T00:00:00Z"),
            closeTime = Instant.EPOCH
        )
        testDataHelper.getTrade(
            tradingAccountId = tradingAccount12.id,
            dealId = "4",
            positionId = "4",
            state = TradeState.CLOSED,
            openTime = Instant.parse("2021-02-01T23:59:59Z"),
            closeTime = Instant.parse("2021-12-24T23:59:59Z")
        )
        testDataHelper.getTrade(
            tradingAccountId = tradingAccount12.id,
            dealId = "5",
            positionId = "5",
            state = TradeState.CLOSED,
            openTime = Instant.parse("2022-12-31T21:00:00Z"),
            closeTime = Instant.parse("2023-01-01T12:59:00Z")
        )
        testDataHelper.getTrade(
            tradingAccountId = tradingAccount41.id,
            dealId = "6",
            positionId = "6",
            state = TradeState.OPEN,
            openTime = fakeTimeNow,
            closeTime = Instant.EPOCH
        )
        testDataHelper.getTrade(
            tradingAccountId = tradingAccount12.id,
            dealId = "7",
            positionId = "7",
            state = TradeState.OPEN,
            openTime = Instant.parse("2023-01-01T20:59:00Z"),
            closeTime = Instant.EPOCH
        )

        testDataHelper.getFutureOrder(
            tradingAccountId = tradingAccount11.id,
            orderId = "111",
            profit = 123.5.toBigDecimal(),
            transactionTime = Instant.parse("2022-12-31T12:00:00Z"),
            symbol = "FTX/USD",
            type = FutureTradeType.MARKET,
            quantity = 3,
            price = 5435.1.toBigDecimal(),
            side = FutureTradeSide.BUY,
            commission = 0.9.toBigDecimal()
        )
        testDataHelper.getFutureOrder(
            tradingAccountId = tradingAccount11.id,
            orderId = "222",
            profit = null,
            transactionTime = Instant.parse("2025-12-31T12:00:00Z"),
            symbol = "FTX/USD",
            type = FutureTradeType.MARKET,
            quantity = 3,
            price = 5435.1.toBigDecimal(),
            side = FutureTradeSide.BUY,
            commission = 0.9.toBigDecimal()
        )

        val certificate1 = testDataHelper.getCertificate(
            tradingAccountId = tradingAccount11.id,
            type = CertificateType.PAYOUT,
            title = "Certificate title 1",
            header = "Certificate header 1",
            subtitle = "Certificate subtitle 1"
        )
        val certificate2 = testDataHelper.getCertificate(
            tradingAccountId = tradingAccount11.id,
            type = CertificateType.EVALUATION_STEP,
            title = "Certificate title 2",
            header = "Certificate header 2",
            subtitle = "Certificate subtitle 2"
        )
        val certificate3 = testDataHelper.getCertificate(
            tradingAccountId = tradingAccount12.id,
            type = CertificateType.LAST_EVALUATION_STEP,
            title = "Certificate title 3",
            header = "Certificate header 3",
            subtitle = "Certificate subtitle 3"
        )
        testDataHelper.getCertificate(
            tradingAccountId = tradingAccount21.id,
            type = CertificateType.TOTAL_PAYOUTS,
            title = "Certificate title 4",
            header = "Certificate header 4",
            subtitle = "Certificate subtitle 4"
        )

        val pdfFile = testDataHelper.getFile(extension = "pdf", type = FileType.USER_CONTRACT)
        val userContract = testDataHelper.getUserContract(
            userId = userId1,
            submissionId = 456L,
            orderId = order1.id,
            embedUrl = "https://example.com/contract/123"
        ) {
            it.markAsSigned(pdfFile.id)
        }

        val result = withConstantNow(fakeTimeNow) {
            queryBus(GetUserOrderDetailQuery(userId = userId1, orderId = order1.id))
        }

        result.let {
            it.id shouldBe order1.id
            it.state shouldBe OrderState.CANCELLED
            it.platform shouldBe PlatformType.META_TRADER_5
            it.platformUrl shouldBe "https://mt5.fortraders.com/terminal"
            it.feedOnboardingUrl shouldBe null

            it.challenge.let { challenge ->
                challenge.id shouldBe challenge1.id
                challenge.name shouldBe "challenge 1"
                challenge.type shouldBe ChallengeType.CRYPTO
                challenge.startingBalance shouldBeEqualComparingTo 111.1.toBigDecimal()

                challenge.plan.let { plan ->
                    plan.id shouldBe challengePlan1.id
                    plan.title shouldBe "challenge plan 1"
                    plan.steps shouldHaveSize 3
                    plan.stepsCount shouldBe 3
                }

                challenge.plan.steps shouldHaveSize 3
                challenge.plan.steps[0].let { step ->
                    step.id shouldBe challengeStep11.id
                    step.number shouldBe 1
                    step.type shouldBe ChallengeStepType.EVALUATION
                    step.profitTarget shouldBe 4
                    step.maxDrawdown shouldBe 14
                    step.dailyDrawdown.shouldBeNull()
                    step.profitSplit.shouldBeNull()
                    step.refund.shouldBeNull()
                    step.payoutsType.shouldBeNull()

                    step.certificates shouldHaveSize 2
                    step.certificates[0].let { certificate ->
                        certificate.id shouldBe certificate1.id
                        certificate.type shouldBe CertificateType.PAYOUT
                        certificate.title shouldBe "Certificate title 1"
                        certificate.header shouldBe "Certificate header 1"
                        certificate.subtitle shouldBe "Certificate subtitle 1"
                    }
                    step.certificates[1].let { certificate ->
                        certificate.id shouldBe certificate2.id
                        certificate.type shouldBe CertificateType.EVALUATION_STEP
                        certificate.title shouldBe "Certificate title 2"
                        certificate.header shouldBe "Certificate header 2"
                        certificate.subtitle shouldBe "Certificate subtitle 2"
                    }

                    step.tradingAccount.shouldNotBeNull()
                }
                challenge.plan.steps[0].tradingAccount.shouldNotBeNull().let { account ->
                    account.id shouldBe tradingAccount11.id
                    account.state shouldBe TradingAccountState.BREACHED
                    account.breach.shouldNotBeNull().let { breach ->
                        breach.breachType shouldBe BreachType.PROFIT_TARGET
                        breach.breachedAt shouldBe Instant.parse("2023-01-02T12:59:00Z")
                    }
                    account.accountId shouldBe "105"
                    account.login shouldBe "105"
                    account.password shouldBe "password11"
                    account.server shouldBe "CapitalMarkets-Live"
                    account.tradingGroup shouldBe "trading group 11"
                    account.profitTarget.let { target ->
                        target.shouldNotBeNull()
                        target shouldBe 25
                    }
                    account.maxDrawdown shouldBe 20
                    account.dailyDrawdown shouldBe 10
                    account.minProfitableTradingDays shouldBe 5
                    account.maxTradingDays shouldBe 55
                    account.leverage shouldBe 15
                    account.startingBalanceOfTheDay shouldBeEqualComparingTo 123.4.toBigDecimal()
                    account.maxDrawdownType shouldBe ChallengeStepMaxDrawdownType.STANDARD
                    account.isLastEvaluationStep shouldBe false
                    account.stats.let { accountStats ->
                        accountStats.totalProfitAmount.value shouldBeEqualComparingTo
                            (600.5.toBigDecimal() - 123.4.toBigDecimal())
                        accountStats.totalProfitAmount.target shouldNotBeNullAndBeEqualComparingTo
                            (123.4.toBigDecimal() * 0.25.toBigDecimal())
                        accountStats.totalProfitAmount.max.shouldBeNull()

                        accountStats.dailyDrawdownAmount.shouldNotBeNull {
                            value shouldBeEqualComparingTo BigDecimal.ZERO
                            target.shouldBeNull()
                            max shouldNotBeNullAndBeEqualComparingTo (123.4.toBigDecimal() * (-0.1).toBigDecimal())
                        }

                        accountStats.maxDrawdownAmount.value shouldBeEqualComparingTo BigDecimal.ZERO
                        accountStats.maxDrawdownAmount.target.shouldBeNull()
                        accountStats.maxDrawdownAmount.max shouldNotBeNullAndBeEqualComparingTo
                            (123.4.toBigDecimal() * (-0.2).toBigDecimal())

                        accountStats.profitableTradingDays.value shouldBe 0
                        accountStats.profitableTradingDays.target shouldBe 5
                        accountStats.profitableTradingDays.max.shouldBeNull()

                        accountStats.dailyPause.shouldNotBeNull {
                            value shouldBeEqualComparingTo BigDecimal.ZERO
                            target.shouldBeNull()
                            max shouldNotBeNullAndBeEqualComparingTo (123.4.toBigDecimal() * (-0.33).toBigDecimal())
                        }

                        accountStats.dailyProfitCap.shouldNotBeNull {
                            value shouldBe (600.5.toBigDecimal() - 123.4.toBigDecimal())
                            target.shouldBeNull()
                            max shouldNotBeNullAndBeEqualComparingTo (123.4.toBigDecimal() * 0.43.toBigDecimal())
                        }

                        accountStats.inactivityPeriodDays.shouldNotBeNull {
                            value shouldBeEqualComparingTo 2L
                            target.shouldBeNull()
                            max shouldBe 329L
                        }
                    }
                    account.history.let { history ->
                        history.balance shouldBeEqualComparingTo 456.7.toBigDecimal()
                        history.equity shouldBeEqualComparingTo 600.5.toBigDecimal()
                        history.data shouldHaveSize 1
                        history.data[0].let { historyData ->
                            historyData.startingBalance shouldBeEqualComparingTo 0.001.toBigDecimal()
                            historyData.balance shouldBeEqualComparingTo 1.111.toBigDecimal()
                            historyData.equity shouldBeEqualComparingTo 2.222.toBigDecimal()
                            historyData.balanceMaxDrawdown shouldBeEqualComparingTo 3.333.toBigDecimal()
                            historyData.balanceDailyDrawdown shouldNotBeNullAndBeEqualComparingTo 4.444.toBigDecimal()
                            historyData.balanceProfitTarget shouldNotBeNullAndBeEqualComparingTo 5.555.toBigDecimal()
                            historyData.date shouldBe tradingAccountHistory1.date
                        }
                    }
                }

                challenge.plan.steps[1].let { step ->
                    step.id shouldBe challengeStep12.id
                    step.number shouldBe 2
                    step.type shouldBe ChallengeStepType.EVALUATION
                    step.profitTarget shouldBe 2
                    step.maxDrawdown shouldBe 12
                    step.dailyDrawdown shouldBe 22
                    step.profitSplit.shouldBeNull()
                    step.refund.shouldBeNull()
                    step.payoutsType.shouldBeNull()

                    step.certificates shouldHaveSize 1
                    step.certificates[0].let { certificate ->
                        certificate.id shouldBe certificate3.id
                        certificate.type shouldBe CertificateType.LAST_EVALUATION_STEP
                        certificate.title shouldBe "Certificate title 3"
                        certificate.header shouldBe "Certificate header 3"
                        certificate.subtitle shouldBe "Certificate subtitle 3"
                    }

                    step.tradingAccount.shouldNotBeNull()
                }
                challenge.plan.steps[1].tradingAccount.shouldNotBeNull().let { account ->
                    account.id shouldBe tradingAccount12.id
                    account.state shouldBe TradingAccountState.ACTIVE
                    account.breach.shouldBeNull()
                    account.accountId shouldBe "106"
                    account.login shouldBe "<EMAIL>"
                    account.password shouldBe "password12"
                    account.server shouldBe "FTLOCK"
                    account.tradingGroup shouldBe "trading group 12"
                    account.profitTarget shouldBe 35
                    account.maxDrawdown shouldBe 30
                    account.dailyDrawdown shouldBe 20
                    account.minProfitableTradingDays shouldBe 15
                    account.consistencyTarget shouldBe 50
                    account.maxTradingDays shouldBe 65
                    account.leverage shouldBe 25
                    account.startingBalanceOfTheDay shouldBeEqualComparingTo 999.99.toBigDecimal()
                    account.maxDrawdownType shouldBe ChallengeStepMaxDrawdownType.TRAILING
                    account.isLastEvaluationStep shouldBe true
                    account.stats.let { accountStats ->
                        accountStats.totalProfitAmount.value shouldBeEqualComparingTo
                            (600.6.toBigDecimal() - 987.6.toBigDecimal())
                        accountStats.totalProfitAmount.target shouldNotBeNullAndBeEqualComparingTo
                            (987.6.toBigDecimal() * 0.35.toBigDecimal())
                        accountStats.totalProfitAmount.max.shouldBeNull()

                        accountStats.dailyDrawdownAmount.shouldNotBeNull {
                            value shouldBeEqualComparingTo (600.6.toBigDecimal() - 999.99.toBigDecimal())
                            target.shouldBeNull()
                            max shouldNotBeNullAndBeEqualComparingTo (999.99.toBigDecimal() * (-0.2).toBigDecimal())
                        }

                        accountStats.maxDrawdownAmount.value shouldBeEqualComparingTo
                            (600.6.toBigDecimal() - 987.6.toBigDecimal())
                        accountStats.maxDrawdownAmount.target.shouldBeNull()
                        accountStats.maxDrawdownAmount.max shouldNotBeNullAndBeEqualComparingTo
                            (987.6.toBigDecimal() * (-0.3).toBigDecimal())

                        accountStats.profitableTradingDays.value shouldBe 4
                        accountStats.profitableTradingDays.target shouldBe 15
                        accountStats.profitableTradingDays.max.shouldBeNull()

                        accountStats.dailyPause.shouldBeNull()

                        accountStats.dailyProfitCap.shouldBeNull()

                        accountStats.consistencyTargetAmount.shouldNotBeNull {
                            value shouldBeEqualComparingTo 15.5.toBigDecimal()
                            max shouldNotBeNullAndBeEqualComparingTo BigDecimal.ZERO
                        }

                        accountStats.consistencyTargetPercentage.shouldNotBeNull {
                            value shouldBeEqualComparingTo BigDecimal.ZERO
                            max shouldNotBeNullAndBeEqualComparingTo 50.toBigDecimal()
                        }
                    }
                    account.history.let { history ->
                        history.balance shouldBeEqualComparingTo 543.2.toBigDecimal()
                        history.equity shouldBeEqualComparingTo 600.6.toBigDecimal()
                        history.data shouldHaveSize 2
                        history.data[0].let { historyData ->
                            historyData.balance shouldBeEqualComparingTo 999.99.toBigDecimal()
                            historyData.equity shouldBeEqualComparingTo 199.99.toBigDecimal()
                            historyData.balanceMaxDrawdown shouldBeEqualComparingTo 299.99.toBigDecimal()
                            historyData.balanceDailyDrawdown.shouldBeNull()
                            historyData.date shouldBe LocalDate.parse("2021-01-01")
                        }
                        history.data[1].let { historyData ->
                            historyData.balance shouldBeEqualComparingTo 543.2.toBigDecimal()
                            historyData.startingBalance shouldBeEqualComparingTo 987.6.toBigDecimal()
                            historyData.equity shouldBeEqualComparingTo 600.6.toBigDecimal()
                            historyData.balanceMaxDrawdown shouldBe (987.6.toBigDecimal() * 0.7.toBigDecimal())
                            historyData.balanceDailyDrawdown shouldNotBeNullAndBeEqualComparingTo
                                (999.99.toBigDecimal() * 0.8.toBigDecimal())
                            historyData.balanceProfitTarget shouldNotBeNullAndBeEqualComparingTo
                                (987.6.toBigDecimal() * 1.35.toBigDecimal())
                            historyData.date shouldBe fakeDateNow
                        }
                    }
                }

                challenge.plan.steps[2].let { step ->
                    step.id shouldBe challengeStep13.id
                    step.number shouldBe 3
                    step.type shouldBe ChallengeStepType.FUNDED
                    step.profitTarget.shouldBeNull()
                    step.maxDrawdown shouldBe 13
                    step.dailyDrawdown shouldBe 23
                    step.profitSplit shouldBe 33
                    step.refund shouldBe 43
                    step.payoutsType shouldBe PayoutsType.BI_WEEKLY
                    step.tradingAccount.shouldBeNull()
                }
            }
            it.breachDiscountCode.shouldNotBeNull {
                id shouldBe discountCode1.id
                code shouldBe "DISCOUNT_CODE_1"
                type shouldBe DiscountCodeType.PERCENTAGE
                percentage shouldBe 10
                amount.shouldBeNull()
            }
            it.userContract.shouldNotBeNull {
                id shouldBe userContract.id
                state shouldBe UserContractState.SIGNED
                embedUrl shouldBe "https://example.com/contract/123"
                createdAt truncatedShouldBe userContract.createdAt
                updatedAt truncatedShouldBe userContract.updatedAt
            }
        }
    }
})
