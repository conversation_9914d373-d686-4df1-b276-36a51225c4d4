package com.cleevio.fortraders.application.module.certificate

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.domain.model.breach.constant.BreachType
import com.cleevio.fortraders.domain.model.certificate.CertificateRepository
import com.cleevio.fortraders.domain.model.certificate.constant.CertificateType
import com.cleevio.fortraders.domain.model.challengestep.constant.ChallengeStepType
import com.cleevio.fortraders.domain.model.payout.constant.PayoutState
import io.kotest.datatest.withData
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe

class CertificateServiceIT(
    private val certificateRepository: CertificateRepository,
    private val underTest: CertificateService,
) : IntegrationTest({

    "should not create certificate because breach type is not profit target" - {
        withData(BreachType.entries.filter { it != BreachType.PROFIT_TARGET && it != BreachType.MANUAL_UPGRADE }) { breachType ->
            underTest.createEvaluationStepCompletedCertificate(
                tradingAccountId = "354479c4-7bf2-4002-ac42-472a2e334557".toUUID(),
                breachType = breachType
            )
        }

        certificateRepository.count() shouldBe 0
    }

    "should not create certificate because challenge step is not evaluation" {
        val challengePlan = testDataHelper.getChallengePlan()
        val challengeStep = testDataHelper.getChallengeStep(challengePlanId = challengePlan.id, type = ChallengeStepType.FUNDED)
        val order = testDataHelper.getOrder(challengeId = challengePlan.challengeId, challengePlanId = challengePlan.id)
        val tradingAccount = testDataHelper.getTradingAccount(
            challengeStepId = challengeStep.id,
            accountId = "105",
            orderId = order.id
        )

        underTest.createEvaluationStepCompletedCertificate(
            tradingAccountId = tradingAccount.id,
            breachType = BreachType.PROFIT_TARGET
        )

        certificateRepository.count() shouldBe 0
    }

    "should correctly create evaluation step certificate" {
        val user = testDataHelper.getUser(firstName = "John", lastName = "Doe")
        val challengePlan = testDataHelper.getChallengePlan(steps = 3)
        val challengeStep = testDataHelper.getChallengeStep(
            challengePlanId = challengePlan.id,
            type = ChallengeStepType.EVALUATION,
            number = 1
        )
        val order = testDataHelper.getOrder(
            userId = user.id,
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id
        )
        val tradingAccount = testDataHelper.getTradingAccount(
            challengeStepId = challengeStep.id,
            accountId = "105",
            orderId = order.id
        )

        underTest.createEvaluationStepCompletedCertificate(
            tradingAccountId = tradingAccount.id,
            breachType = BreachType.PROFIT_TARGET
        )

        val certificates = certificateRepository.findAll()
        certificates shouldHaveSize 1
        certificates[0].let {
            it.tradingAccountId shouldBe tradingAccount.id
            it.type shouldBe CertificateType.EVALUATION_STEP
            it.header shouldBe "John D."
            it.title shouldBe "Passed"
            it.subtitle shouldBe "ForTraders Challenge"
        }
    }

    "should correctly create last evaluation step certificate" {
        val user = testDataHelper.getUser(firstName = "John", lastName = "Doe")
        val challengePlan = testDataHelper.getChallengePlan(steps = 3)
        val challengeStep = testDataHelper.getChallengeStep(
            challengePlanId = challengePlan.id,
            type = ChallengeStepType.EVALUATION,
            number = 2
        )
        val order = testDataHelper.getOrder(
            userId = user.id,
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id
        )
        val tradingAccount = testDataHelper.getTradingAccount(
            challengeStepId = challengeStep.id,
            accountId = "105",
            orderId = order.id,
            isLastEvaluationStep = true
        )

        underTest.createEvaluationStepCompletedCertificate(
            tradingAccountId = tradingAccount.id,
            breachType = BreachType.PROFIT_TARGET
        )

        val certificates = certificateRepository.findAll()
        certificates shouldHaveSize 1
        certificates[0].let {
            it.tradingAccountId shouldBe tradingAccount.id
            it.type shouldBe CertificateType.LAST_EVALUATION_STEP
            it.header shouldBe "John D."
            it.title shouldBe "Passed"
            it.subtitle shouldBe "ForTraders Challenge"
        }
    }

    "should correctly create payout certificate and also total payout certificate" {
        val user = testDataHelper.getUser(firstName = "John", lastName = "Doe")
        val challengePlan = testDataHelper.getChallengePlan()
        val challengeStep = testDataHelper.getChallengeStep(challengePlanId = challengePlan.id)
        val order = testDataHelper.getOrder(
            userId = user.id,
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id
        )
        val tradingAccount = testDataHelper.getTradingAccount(
            challengeStepId = challengeStep.id,
            accountId = "105",
            orderId = order.id,
            profitSplit = 50,
            isFundedChallengeStep = true
        )
        val payout = testDataHelper.getPayout(
            tradingAccountId = tradingAccount.id,
            amount = 1000L.toBigDecimal(),
            profitSplit = 50
        )

        underTest.createAndUpdateApprovedPayoutCertificates(
            tradingAccountId = tradingAccount.id,
            payoutId = payout.id
        )

        val certificates = certificateRepository.findAll()
        certificates shouldHaveSize 2
        certificates[0].let {
            it.tradingAccountId shouldBe tradingAccount.id
            it.type shouldBe CertificateType.PAYOUT
            it.header shouldBe "John D."
            it.title shouldBe "$500.00"
            it.subtitle shouldBe "Profit Share"
        }
        certificates[1].let {
            it.tradingAccountId shouldBe tradingAccount.id
            it.type shouldBe CertificateType.TOTAL_PAYOUTS
            it.header shouldBe "John D."
            it.title shouldBe "$500.00"
            it.subtitle shouldBe "Total Payouts"
        }
    }

    "should correctly create payout certificate and also update total payout certificate" {
        val user = testDataHelper.getUser(firstName = "John", lastName = "Doe")
        val challengePlan = testDataHelper.getChallengePlan()
        val challengeStep = testDataHelper.getChallengeStep(challengePlanId = challengePlan.id)
        val order = testDataHelper.getOrder(
            userId = user.id,
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id
        )
        val tradingAccount = testDataHelper.getTradingAccount(
            challengeStepId = challengeStep.id,
            accountId = "105",
            orderId = order.id,
            profitSplit = 50,
            isFundedChallengeStep = true
        )
        val payout1 = testDataHelper.getPayout(
            tradingAccountId = tradingAccount.id,
            amount = 1000L.toBigDecimal(),
            state = PayoutState.APPROVED,
            profitSplit = 50
        )
        val payout2 = testDataHelper.getPayout(
            tradingAccountId = tradingAccount.id,
            amount = 2000L.toBigDecimal(),
            state = PayoutState.APPROVED,
            profitSplit = 50
        )
        testDataHelper.getPayout(
            tradingAccountId = tradingAccount.id,
            amount = 3000L.toBigDecimal(),
            state = PayoutState.REQUESTED,
            profitSplit = 50
        )
        testDataHelper.getCertificate(
            tradingAccountId = tradingAccount.id,
            type = CertificateType.TOTAL_PAYOUTS,
            title = "Malformed title"
        )

        underTest.createAndUpdateApprovedPayoutCertificates(
            tradingAccountId = tradingAccount.id,
            payoutId = payout2.id
        )

        val certificates = certificateRepository.findAll()
        certificates shouldHaveSize 2
        certificates[0].let {
            it.tradingAccountId shouldBe tradingAccount.id
            it.type shouldBe CertificateType.PAYOUT
            it.header shouldBe "John D."
            it.title shouldBe "$1,000.00"
            it.subtitle shouldBe "Profit Share"
        }
        certificates[1].let {
            it.tradingAccountId shouldBe tradingAccount.id
            it.type shouldBe CertificateType.TOTAL_PAYOUTS
            it.title shouldBe "$1,500.00"
        }
    }
})
