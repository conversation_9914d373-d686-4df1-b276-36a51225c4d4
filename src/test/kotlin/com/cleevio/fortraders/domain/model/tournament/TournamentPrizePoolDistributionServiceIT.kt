package com.cleevio.fortraders.domain.model.tournament

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.domain.model.tournament.constant.TournamentReward
import com.cleevio.fortraders.domain.model.transaction.TransactionRepository
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionType
import com.cleevio.fortraders.domain.model.wallet.WalletRepository
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import java.time.Instant
import java.time.temporal.ChronoUnit

class TournamentPrizePoolDistributionServiceIT(
    private val transactionRepository: TransactionRepository,
    private val walletRepository: WalletRepository,
    private val underTest: TournamentPrizePoolDistributionService,
) : IntegrationTest({

    "should correctly distribute prize pool to best 3 tournament users" {
        val user1 = testDataHelper.getUser(index = 1)
        val user2 = testDataHelper.getUser(index = 2)
        val user3 = testDataHelper.getUser(index = 3)
        val user4 = testDataHelper.getUser(index = 4)
        val user5 = testDataHelper.getUser(index = 5)

        val wallet1 = testDataHelper.getWallet(userId = user1.id) { it.addToBalance(10.0.toBigDecimal()) }
        val wallet2 = testDataHelper.getWallet(userId = user2.id) { it.addToBalance(20.0.toBigDecimal()) }
        val wallet3 = testDataHelper.getWallet(userId = user3.id) { it.addToBalance(30.0.toBigDecimal()) }
        val wallet4 = testDataHelper.getWallet(userId = user4.id) { it.addToBalance(40.0.toBigDecimal()) }
        val wallet5 = testDataHelper.getWallet(userId = user5.id) { it.addToBalance(50.0.toBigDecimal()) }

        val tournament1 = testDataHelper.getTournament(
            initialPrizePool = 10000.0.toBigDecimal(),
            reward = TournamentReward.MONEY,
            endsAt = Instant.now().minus(1, ChronoUnit.DAYS)
        )
        val tournament2 = testDataHelper.getTournament(
            initialPrizePool = 9999999L.toBigDecimal(),
            reward = TournamentReward.MONEY,
            endsAt = Instant.now().minus(1, ChronoUnit.DAYS)
        )

        testDataHelper.getTournamentUser(
            tournamentId = tournament1.id,
            userId = user1.id,
            totalProfitPercentage = 0.5.toBigDecimal()
        )
        testDataHelper.getTournamentUser(
            tournamentId = tournament1.id,
            userId = user2.id,
            totalProfitPercentage = 2.51.toBigDecimal()
        )
        testDataHelper.getTournamentUser(
            tournamentId = tournament1.id,
            userId = user3.id,
            totalProfitPercentage = 2.5.toBigDecimal()
        )
        testDataHelper.getTournamentUser(tournamentId = tournament1.id, userId = user4.id, totalProfitPercentage = null)
        testDataHelper.getTournamentUser(
            tournamentId = tournament1.id,
            userId = user5.id,
            totalProfitPercentage = (-3.1).toBigDecimal()
        )
        transactionRepository.findAllByType(TransactionType.TOURNAMENT_PRIZE_DEPOSIT) shouldHaveSize 0

        underTest.distributePrizePool(tournament1.id)

        val transactions = transactionRepository.findAllByType(TransactionType.TOURNAMENT_PRIZE_DEPOSIT).sortedBy { it.createdAt }
        transactions shouldHaveSize 3
        transactions[0].let {
            it.walletId shouldBe wallet2.id
            // 0.18 * 10000.0
            it.amount shouldBeEqualComparingTo 1800L.toBigDecimal()
            it.type shouldBe TransactionType.TOURNAMENT_PRIZE_DEPOSIT
        }
        transactions[1].let {
            it.walletId shouldBe wallet3.id
            // 0.14 * 10000.0
            it.amount shouldBeEqualComparingTo 1400L.toBigDecimal()
            it.type shouldBe TransactionType.TOURNAMENT_PRIZE_DEPOSIT
        }
        transactions[2].let {
            it.walletId shouldBe wallet1.id
            // 0.12 * 10000.0
            it.amount shouldBeEqualComparingTo 1200L.toBigDecimal()
            it.type shouldBe TransactionType.TOURNAMENT_PRIZE_DEPOSIT
        }

        val wallets = walletRepository.findAll().sortedByDescending { it.balance }
        wallets shouldHaveSize 5
        wallets[0].let {
            it.userId shouldBe user2.id
            it.balance shouldBeEqualComparingTo 1820L.toBigDecimal()
        }
        wallets[1].let {
            it.userId shouldBe user3.id
            it.balance shouldBeEqualComparingTo 1430L.toBigDecimal()
        }
        wallets[2].let {
            it.userId shouldBe user1.id
            it.balance shouldBeEqualComparingTo 1210L.toBigDecimal()
        }
        wallets[3].let {
            it.userId shouldBe user5.id
            it.balance shouldBeEqualComparingTo 50L.toBigDecimal()
        }
        wallets[4].let {
            it.userId shouldBe user4.id
            it.balance shouldBeEqualComparingTo 40L.toBigDecimal()
        }
    }

    "should correctly distribute prize pool to best 3 tournament users with profit" {
        val user1 = testDataHelper.getUser(index = 1)
        val user2 = testDataHelper.getUser(index = 2)
        val user3 = testDataHelper.getUser(index = 3)
        val user4 = testDataHelper.getUser(index = 4)
        val user5 = testDataHelper.getUser(index = 5)

        val wallet1 = testDataHelper.getWallet(userId = user1.id) { it.addToBalance(10.0.toBigDecimal()) }
        val wallet2 = testDataHelper.getWallet(userId = user2.id) { it.addToBalance(20.0.toBigDecimal()) }
        val wallet3 = testDataHelper.getWallet(userId = user3.id) { it.addToBalance(30.0.toBigDecimal()) }
        val wallet4 = testDataHelper.getWallet(userId = user4.id) { it.addToBalance(40.0.toBigDecimal()) }
        val wallet5 = testDataHelper.getWallet(userId = user5.id) { it.addToBalance(50.0.toBigDecimal()) }

        val tournament1 = testDataHelper.getTournament(
            initialPrizePool = 10000.0.toBigDecimal(),
            reward = TournamentReward.MONEY,
            endsAt = Instant.now().minus(1, ChronoUnit.DAYS)
        )
        val tournament2 = testDataHelper.getTournament(
            initialPrizePool = 9999999L.toBigDecimal(),
            reward = TournamentReward.MONEY,
            endsAt = Instant.now().minus(1, ChronoUnit.DAYS)
        )

        testDataHelper.getTournamentUser(
            tournamentId = tournament1.id,
            userId = user1.id,
            totalProfitPercentage = 0.5.toBigDecimal()
        )
        testDataHelper.getTournamentUser(
            tournamentId = tournament1.id,
            userId = user2.id,
            totalProfitPercentage = 2.51.toBigDecimal()
        )
        testDataHelper.getTournamentUser(
            tournamentId = tournament1.id,
            userId = user3.id,
            totalProfitPercentage = (-2.5).toBigDecimal()
        )
        testDataHelper.getTournamentUser(tournamentId = tournament1.id, userId = user4.id, totalProfitPercentage = null)
        testDataHelper.getTournamentUser(
            tournamentId = tournament2.id,
            userId = user5.id,
            totalProfitPercentage = 99.99.toBigDecimal()
        )
        transactionRepository.findAllByType(TransactionType.TOURNAMENT_PRIZE_DEPOSIT) shouldHaveSize 0

        underTest.distributePrizePool(tournament1.id)

        val transactions = transactionRepository.findAllByType(TransactionType.TOURNAMENT_PRIZE_DEPOSIT).sortedBy { it.createdAt }
        transactions shouldHaveSize 2
        transactions[0].let {
            it.walletId shouldBe wallet2.id
            // 0.18 * 10000.0
            it.amount shouldBeEqualComparingTo 1800L.toBigDecimal()
            it.type shouldBe TransactionType.TOURNAMENT_PRIZE_DEPOSIT
        }
        transactions[1].let {
            it.walletId shouldBe wallet1.id
            // 0.14 * 10000.0
            it.amount shouldBeEqualComparingTo 1400L.toBigDecimal()
            it.type shouldBe TransactionType.TOURNAMENT_PRIZE_DEPOSIT
        }

        val wallets = walletRepository.findAll().sortedByDescending { it.balance }
        wallets shouldHaveSize 5
        wallets[0].let {
            it.userId shouldBe user2.id
            it.balance shouldBeEqualComparingTo 1820L.toBigDecimal()
        }
        wallets[1].let {
            it.userId shouldBe user1.id
            it.balance shouldBeEqualComparingTo 1410L.toBigDecimal()
        }
        wallets[2].let {
            it.userId shouldBe user5.id
            it.balance shouldBeEqualComparingTo 50L.toBigDecimal()
        }
        wallets[3].let {
            it.userId shouldBe user4.id
            it.balance shouldBeEqualComparingTo 40L.toBigDecimal()
        }
        wallets[4].let {
            it.userId shouldBe user3.id
            it.balance shouldBeEqualComparingTo 30L.toBigDecimal()
        }
    }

    "should do nothing because tournament has not ended" {
        val user1 = testDataHelper.getUser(index = 1)
        val wallet1 = testDataHelper.getWallet(userId = user1.id) { it.addToBalance(1000.0.toBigDecimal()) }

        val tournament1 = testDataHelper.getTournament(initialPrizePool = 1000.0.toBigDecimal(), reward = TournamentReward.MONEY)
        val tournament2 = testDataHelper.getTournament(
            initialPrizePool = 99999L.toBigDecimal(),
            reward = TournamentReward.MONEY,
            endsAt = Instant.now().minus(1, ChronoUnit.DAYS)
        )

        testDataHelper.getTournamentUser(
            tournamentId = tournament1.id,
            userId = user1.id,
            totalProfitPercentage = 0.5.toBigDecimal()
        )
        transactionRepository.findAllByType(TransactionType.TOURNAMENT_PRIZE_DEPOSIT) shouldHaveSize 0

        underTest.distributePrizePool(tournament1.id)

        transactionRepository.findAllByType(TransactionType.TOURNAMENT_PRIZE_DEPOSIT) shouldHaveSize 0
    }

    "should do nothing because tournament prize is not MONEY" {
        val user1 = testDataHelper.getUser(index = 1)
        val wallet1 = testDataHelper.getWallet(userId = user1.id) { it.addToBalance(1000.0.toBigDecimal()) }

        val tournament1 = testDataHelper.getTournament(
            initialPrizePool = 1000.0.toBigDecimal(),
            reward = TournamentReward.CHALLENGES,
            endsAt = Instant.now().minus(1, ChronoUnit.DAYS)
        )
        val tournament2 = testDataHelper.getTournament(
            initialPrizePool = 99999L.toBigDecimal(),
            endsAt = Instant.now().minus(1, ChronoUnit.DAYS)
        )

        testDataHelper.getTournamentUser(
            tournamentId = tournament1.id,
            userId = user1.id,
            totalProfitPercentage = 0.5.toBigDecimal()
        )
        transactionRepository.findAllByType(TransactionType.TOURNAMENT_PRIZE_DEPOSIT) shouldHaveSize 0

        underTest.distributePrizePool(tournament1.id)

        transactionRepository.findAllByType(TransactionType.TOURNAMENT_PRIZE_DEPOSIT) shouldHaveSize 0
    }
})
