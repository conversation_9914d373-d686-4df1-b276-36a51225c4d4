package com.cleevio.fortraders.domain.model.challenge

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.domain.model.challenge.constant.ChallengeState
import com.cleevio.fortraders.domain.model.challenge.constant.ChallengeType
import com.cleevio.fortraders.truncatedShouldBe
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.date.shouldBeAfter
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import java.time.Instant

class ChallengeCreateServiceIT(
    private val challengeRepository: ChallengeRepository,
    private val underTest: ChallengeCreateService,
) : IntegrationTest({

    "should create challenge" {
        challengeRepository.count() shouldBe 0
        val timeBeforeTestStart = Instant.now()

        underTest.create(
            name = "Mission impossible",
            startingBalance = 105.5.toBigDecimal(),
            type = ChallengeType.FOREX
        )

        val challenges = challengeRepository.findAll()
        challenges shouldHaveSize 1
        challenges[0].let {
            it.name shouldBe "Mission impossible"
            it.startingBalance shouldBeEqualComparingTo 105.5.toBigDecimal()
            it.state shouldBe ChallengeState.DISABLED
            it.type shouldBe ChallengeType.FOREX
            it.createdAt shouldBeAfter timeBeforeTestStart
            it.updatedAt truncatedShouldBe it.createdAt
            it.createdBy.shouldBeNull()
            it.updatedBy.shouldBeNull()
        }
    }
})
