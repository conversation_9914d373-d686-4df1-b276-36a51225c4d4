package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.ControllerTest
import com.cleevio.fortraders.addAcceptHeader
import com.cleevio.fortraders.addBearerAuthHeader
import com.cleevio.fortraders.addJsonContent
import com.cleevio.fortraders.application.common.command.IdResult
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.application.module.affiliate.command.AdminPatchAffiliateUserCommand
import com.cleevio.fortraders.application.module.affiliate.command.AdminUpgradeUserToAffiliatePartnerCommand
import com.cleevio.fortraders.application.module.affiliate.query.AdminSearchAffiliatesQuery
import com.cleevio.fortraders.domain.model.affiliate.constant.AffiliateState
import com.cleevio.fortraders.jsonContent
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.test.web.servlet.patch
import org.springframework.test.web.servlet.post
import java.time.Instant
import java.util.Optional

@WebMvcTest(AdminAffiliateUsersController::class)
class AdminAffiliateUsersControllerTest : ControllerTest({

    beforeAny {
        every { jwtDecoder.decode(any()) } returns admin.jwt
    }

    "should correctly access upgrade user as affiliate EP" {
        every { commandBus(any<AdminUpgradeUserToAffiliatePartnerCommand>()) } returns IdResult(
            id = "2fa7b171-5094-44dc-a0e3-f5916ae49956".toUUID()
        )

        mockMvc.post("/admin-app/users/3af140b3-e631-4a1e-b664-7c3acb10a631/affiliate") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(admin.accessToken)
            addJsonContent(
                """
                {
                    "couponCode": "may",
                    "discountAmountPercentage": 10,
                    "commissionPercentage": 5,
                    "note": "test"
                }
                """.trimIndent()
            )
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                {
                    "id": "2fa7b171-5094-44dc-a0e3-f5916ae49956"
                }
                """.trimIndent()
            )
        }

        verify {
            commandBus(
                AdminUpgradeUserToAffiliatePartnerCommand(
                    userId = "3af140b3-e631-4a1e-b664-7c3acb10a631".toUUID(),
                    couponCode = "MAY",
                    discountAmountPercentage = 10,
                    commissionPercentage = 5,
                    note = "test"
                )
            )
            jwtDecoder.decode(admin.accessToken)
        }
    }

    "should correctly access patch affiliate user EP" {
        every { commandBus(any<AdminPatchAffiliateUserCommand>()) } just runs

        mockMvc.patch("/admin-app/users/3af140b3-e631-4a1e-b664-7c3acb10a631/affiliate") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(admin.accessToken)
            addJsonContent(
                """
                {
                    "state": "ACTIVE",
                    "couponCode": "coupon_code",
                    "commissionPercentage": 5,
                    "discountAmountPercentage": 10,
                    "note": "test"
                }
                """.trimIndent()
            )
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            commandBus(
                AdminPatchAffiliateUserCommand(
                    userId = "3af140b3-e631-4a1e-b664-7c3acb10a631".toUUID(),
                    state = AffiliateState.ACTIVE,
                    couponCode = "COUPON_CODE",
                    commissionPercentage = 5,
                    discountAmountPercentage = 10,
                    note = Optional.of("test")
                )
            )
            jwtDecoder.decode(admin.accessToken)
        }
    }

    "should correctly access search affiliate users EP" {
        every { queryBus(any<AdminSearchAffiliatesQuery>()) } returns PageImpl(
            listOf(
                AdminSearchAffiliatesQuery.Result(
                    id = "db22f458-033d-42f6-bcbb-74cc4be53e79".toUUID(),
                    state = AffiliateState.ACTIVE,
                    couponCode = "MAY",
                    discountAmountPercentage = 10,
                    commissionPercentage = 5,
                    referralCount = 25,
                    commissionBalance = 100.5.toBigDecimal(),
                    note = "test",
                    createdAt = Instant.parse("2023-01-06T21:44:13.064778Z"),
                    updatedAt = Instant.parse("2024-02-06T21:44:13.064778Z"),
                    user = AdminSearchAffiliatesQuery.UserDetail(
                        id = "902df4e6-5a28-4b8a-8531-e8a0ac47d90d".toUUID(),
                        email = "<EMAIL>",
                        firstName = "John",
                        lastName = "Doe",
                        blacklisted = false
                    )
                )
            ),
            PageRequest.of(1, 10),
            2
        )

        mockMvc.post("/admin-app/users/affiliates/search?page=1&size=10") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(admin.accessToken)
            addJsonContent(
                """
                {
                    "referralCountFrom": 1,
                    "referralCountTo": 100,
                    "states": ["ACTIVE"],
                    "fulltext": "john"
                }
                """.trimIndent()
            )
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                {
                	"content": [{
                		"id": "db22f458-033d-42f6-bcbb-74cc4be53e79",
                		"state": "ACTIVE",
                		"couponCode": "MAY",
                		"discountAmountPercentage": 10,
                		"commissionPercentage": 5,
                		"referralCount": 25,
                		"commissionBalance": 100.5,
                		"note": "test",
                		"createdAt": "2023-01-06T21:44:13.064778Z",
                		"updatedAt": "2024-02-06T21:44:13.064778Z",
                		"user": {
                			"id": "902df4e6-5a28-4b8a-8531-e8a0ac47d90d",
                			"email": "<EMAIL>",
                 			"firstName": "John",
                            "lastName": "Doe",
                            "blacklisted": false
                        }
                	}],
                	"currentPage": 1,
                	"pageSize": 10,
                	"totalElements": 11,
                	"totalPages": 2
                }
                """.trimIndent()
            )
        }

        verify {
            queryBus(
                AdminSearchAffiliatesQuery(
                    pageable = PageRequest.of(1, 10),
                    filter = AdminSearchAffiliatesQuery.Filter(
                        referralCountFrom = 1,
                        referralCountTo = 100,
                        states = setOf(AffiliateState.ACTIVE),
                        fulltext = "john"
                    )
                )
            )
            jwtDecoder.decode(admin.accessToken)
        }
    }
})
