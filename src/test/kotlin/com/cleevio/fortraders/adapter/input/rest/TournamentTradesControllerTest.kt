package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.ControllerTest
import com.cleevio.fortraders.addAcceptHeader
import com.cleevio.fortraders.addBearerAuthHeader
import com.cleevio.fortraders.addJsonContent
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.application.module.tournamenttrade.command.CloseTournamentTradeCommand
import com.cleevio.fortraders.application.module.tournamenttrade.command.CreateTournamentTradeCommand
import com.cleevio.fortraders.domain.model.tournamenttrade.constant.TournamentTradeType
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.test.web.servlet.post

@WebMvcTest(TournamentTradesController::class)
class TournamentTradesControllerTest : ControllerTest({

    beforeAny {
        every { jwtDecoder.decode(any()) } returns user.jwt
    }

    "should correctly access create tournament trade EP" {
        every { commandBus(any<CreateTournamentTradeCommand>()) } just runs

        val gameId = "9bb9aa51-1681-46f7-973a-a9a01750ae6d".toUUID()
        val tournamentId = "4930717e-259c-4f6f-9f8b-414a844851ff".toUUID()

        mockMvc.post("/web-app/games/$gameId/tournaments/$tournamentId/trades") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(user.accessToken)
            addJsonContent(
                """
                {
                    "instrumentId": "dfbc4fba-a305-45b8-8405-34baf60f2744",
                    "type": "BUY",
                    "leverage": 3,
                    "stopLoss": 1.0,
                    "takeProfit": 2.0
                }
                """.trimIndent()
            )
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            commandBus(
                CreateTournamentTradeCommand(
                    userId = user.appUserId,
                    tournamentId = tournamentId,
                    gameId = gameId,
                    instrumentId = "dfbc4fba-a305-45b8-8405-34baf60f2744".toUUID(),
                    type = TournamentTradeType.BUY,
                    leverage = 3,
                    stopLoss = 1.0.toBigDecimal(),
                    takeProfit = 2.0.toBigDecimal()
                )
            )
            jwtDecoder.decode(user.accessToken)
        }
    }

    "should correctly access close tournament trade EP" {
        every { commandBus(any<CloseTournamentTradeCommand>()) } just runs

        val gameId = "9bb9aa51-1681-46f7-973a-a9a01750ae6d".toUUID()
        val tournamentId = "4930717e-259c-4f6f-9f8b-414a844851ff".toUUID()
        val tournamentTradeId = "099c471f-00e1-44d6-976a-47485f0a9837".toUUID()

        mockMvc.post("/web-app/games/$gameId/tournaments/$tournamentId/trades/$tournamentTradeId/close") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(user.accessToken)
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            commandBus(
                CloseTournamentTradeCommand(
                    userId = user.appUserId,
                    tournamentId = tournamentId,
                    gameId = gameId,
                    tournamentTradeId = tournamentTradeId
                )
            )
            jwtDecoder.decode(user.accessToken)
        }
    }
})
