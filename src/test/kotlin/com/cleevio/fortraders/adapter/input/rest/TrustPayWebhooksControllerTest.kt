package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.ControllerTest
import com.cleevio.fortraders.application.module.transaction.command.ProcessPaymentGatewayPaymentResultCommand
import com.cleevio.fortraders.domain.model.transaction.constant.PaymentGatewayProvider
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.test.web.servlet.get

@WebMvcTest(TrustPayWebhooksController::class)
class TrustPayWebhooksControllerTest : ControllerTest({

    "should throw because accessing process payment result EP without signature" {
        mockMvc.get(
            "/webhooks/trust-pay" +
                "?AccountId=**********" +
                "&Amount=99.12" +
                "&Currency=EUR" +
                "&Reference=f4019d01ce9c4fafa0d212faa8b789ae" +
                "&Type=CRDT" +
                "&ResultCode=3" +
                "&PaymentRequestId=**********" +
                "&CardMask=420000******0000" +
                "&CardExpiration=1225"
        ).andExpect {
            status { isUnauthorized() }
        }
    }

    "should throw because accessing process payment result EP with invalid signature" {
        mockMvc.get(
            "/webhooks/trust-pay" +
                "?AccountId=**********" +
                "&Amount=99.12" +
                "&Currency=EUR" +
                "&Reference=f4019d01ce9c4fafa0d212faa8b789ae" +
                "&Type=CRDT" +
                "&ResultCode=3" +
                "&PaymentRequestId=**********" +
                "&CardMask=420000******0000" +
                "&CardExpiration=1225" +
                "&Signature=17B6BC71C90FF810F5ADE70DB2BA51561E95B7925E1296DB5107C911CF411DC9AAAAAAA"
        ).andExpect {
            status { isUnauthorized() }
        }
    }

    "should correctly access process payment result EP" {
        every { commandBus(any<ProcessPaymentGatewayPaymentResultCommand>()) } just runs

        mockMvc.get(
            "/webhooks/trust-pay" +
                "?AccountId=**********" +
                "&Amount=99.12" +
                "&Currency=EUR" +
                "&Reference=f4019d01ce9c4fafa0d212faa8b789ae" +
                "&Type=CRDT" +
                "&ResultCode=3" +
                "&PaymentRequestId=**********" +
                "&CardMask=420000******0000" +
                "&CardExpiration=1225" +
                "&Signature=17B6BC71C90FF810F5ADE70DB2BA51561E95B7925E1296DB5107C911CF411DC9"
        ).andExpect {
            status { isOk() }
        }

        verify {
            commandBus(
                ProcessPaymentGatewayPaymentResultCommand(
                    reference = "f4019d01ce9c4fafa0d212faa8b789ae",
                    result = "3",
                    paymentGatewayProvider = PaymentGatewayProvider.TRUST_PAY
                )
            )
        }
    }
})
