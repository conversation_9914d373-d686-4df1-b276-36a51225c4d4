package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.ControllerTest
import com.cleevio.fortraders.addAcceptHeader
import com.cleevio.fortraders.addBearerAuthHeader
import com.cleevio.fortraders.addJsonContent
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.application.module.challengestepsetting.command.AdminPatchChallengeStepSettingCommand
import com.cleevio.fortraders.domain.model.challengestepsetting.constant.ChallengeStepSettingType
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.test.web.servlet.patch

@WebMvcTest(AdminChallengeStepSettingsController::class)
class AdminChallengeStepSettingsControllerTest : ControllerTest({

    beforeAny {
        every { jwtDecoder.decode(any()) } returns admin.jwt
    }

    "should correctly access patch challenge step setting EP" {
        every { commandBus(any<AdminPatchChallengeStepSettingCommand>()) } just runs

        val challengeId = "ab59bbbb-0520-4f8e-bf46-28de66198421".toUUID()
        val challengePlanId = "32bcc464-e423-4ca0-ac0a-3e22864dd636".toUUID()
        mockMvc.patch("/admin-app/challenges/$challengeId/plans/$challengePlanId/steps/2/settings/PAYOUTS") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(admin.accessToken)
            addJsonContent(
                """
                {
                    "useMovementsFromOrder": true,
                    "isVisibleInConfigurator": false
                }
                """.trimIndent()
            )
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            commandBus(
                AdminPatchChallengeStepSettingCommand(
                    challengePlanId = "32bcc464-e423-4ca0-ac0a-3e22864dd636".toUUID(),
                    challengeStepNumber = 2,
                    challengeStepSettingType = ChallengeStepSettingType.PAYOUTS,
                    useMovementsFromOrder = true,
                    isVisibleInConfigurator = false
                )
            )
            jwtDecoder.decode(admin.accessToken)
        }
    }
})
