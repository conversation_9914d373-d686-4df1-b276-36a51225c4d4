package com.cleevio.fortraders.adapter.output.myfatoorah

import com.cleevio.fortraders.ConnectorTest
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.bodyShouldBeJson
import com.cleevio.fortraders.domain.model.transaction.constant.Currency
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionSubscriptionInterval
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import org.mockserver.model.HttpRequest
import org.mockserver.model.HttpResponse
import org.mockserver.model.MediaType

class MyFatoorahConnectorTest : ConnectorTest({
    val underTest = MyFatoorahConnector(
        baseUrl = baseUrl,
        apiKey = "my-fatoorah-api-key",
        subscriptionInterval = TransactionSubscriptionInterval.MONTHLY
    )

    "should correctly initiate session" {
        client.`when`(
            HttpRequest.request()
        ).respond(
            HttpResponse.response()
                .withStatusCode(200)
                .withBody(INITIATE_SESSION_RESPONSE, MediaType.APPLICATION_JSON)
        )

        val result = underTest.initiateSession(isRecurring = false)
        result shouldBe "a76202c2-60ae-4b89-bce1-9b81fae35d03"

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/v2/InitiateSession"
            it.method shouldBe "POST"
            it.assertHeaders()

            it.bodyShouldBeJson(
                """
                {
                    "IsRecurring": false
                }
                """.trimIndent()
            )
        }
    }

    "should correctly execute payment that is not subscription" {
        client.`when`(
            HttpRequest.request()
                .withPath("/v2/ExecutePayment")
        ).respond(
            HttpResponse.response()
                .withStatusCode(200)
                .withBody(EXECUTE_PAYMENT_RESPONSE, MediaType.APPLICATION_JSON)
        )

        val result = underTest.executePayment(
            sessionId = "42005017-87ad-4be5-a59a-a0892606322d",
            transactionId = "b34d6dbf-d20d-4b2b-8611-a4be4e930364".toUUID(),
            amount = 100.5.toBigDecimal(),
            currency = Currency.USD,
            isSubscription = false,
            userFullName = "John Doe",
            email = "<EMAIL>"
        )
        result.paymentUrl shouldBe "https://demo.MyFatoorah.com/En/KWT/PayInvoice/MpgsAuthentication?paymentId=07074538212227573"
        result.subscriptionId.shouldBeNull()

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/v2/ExecutePayment"
            it.method shouldBe "POST"
            it.assertHeaders()

            it.bodyShouldBeJson(
                """
                {
                    "InvoiceValue": 100.5,
                    "SessionId": "42005017-87ad-4be5-a59a-a0892606322d",
                    "CustomerReference": "b34d6dbf-d20d-4b2b-8611-a4be4e930364",
                    "DisplayCurrencyIso": "USD",
                    "Language": "EN",
                    "CustomerName": "John Doe",
                    "CustomerEmail": "<EMAIL>"
                }
                """.trimIndent()
            )
        }
    }

    "should correctly execute payment that is subscription" {
        client.`when`(
            HttpRequest.request()
                .withPath("/v2/ExecutePayment")
        ).respond(
            HttpResponse.response()
                .withStatusCode(200)
                .withBody(EXECUTE_SUBSCRIPTION_PAYMENT_RESPONSE, MediaType.APPLICATION_JSON)
        )

        val result = underTest.executePayment(
            sessionId = "42005017-87ad-4be5-a59a-a0892606322d",
            transactionId = "b34d6dbf-d20d-4b2b-8611-a4be4e930364".toUUID(),
            amount = 100.5.toBigDecimal(),
            currency = Currency.USD,
            isSubscription = true,
            userFullName = "John Doe",
            email = "<EMAIL>"
        )
        result.paymentUrl shouldBe "https://demo.MyFatoorah.com/En/KWT/PayInvoice/MpgsAuthentication?paymentId=07074538212227573"
        result.subscriptionId shouldBe "456"

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/v2/ExecutePayment"
            it.method shouldBe "POST"
            it.assertHeaders()

            it.bodyShouldBeJson(
                """
                {
                    "InvoiceValue": 100.5,
                    "SessionId": "42005017-87ad-4be5-a59a-a0892606322d",
                    "CustomerReference": "b34d6dbf-d20d-4b2b-8611-a4be4e930364",
                    "DisplayCurrencyIso": "USD",
                    "Language": "EN",
                    "RecurringModel": {
                        "RecurringType": "Custom",
                        "RetryCount": 3,
                        "IntervalDays": 29
                    },
                    "CustomerName": "John Doe",
                    "CustomerEmail": "<EMAIL>"
                }
                """.trimIndent()
            )
        }
    }

    "should correctly cancel subscription" {
        client.`when`(
            HttpRequest.request()
        ).respond(
            HttpResponse.response()
                .withStatusCode(200)
        )

        underTest.cancelSubscription(
            subscriptionId = "123456"
        )

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/v2/CancelRecurringPayment"
            it.method shouldBe "POST"
            it.assertHeaders()

            it.bodyShouldBeJson("{}")

            it.queryStringParameterList shouldHaveSize 1
            it.queryStringParameterList[0].let { param ->
                param.name.value shouldBe "recurringId"
                param.values shouldHaveSize 1
                param.values[0] shouldBe "123456"
            }
        }
    }
})

private fun HttpRequest.assertHeaders() {
    containsHeader("Accept", "application/json") shouldBe true
    containsHeader("Authorization", "Bearer my-fatoorah-api-key") shouldBe true
}

private const val INITIATE_SESSION_RESPONSE = """
{
    "IsSuccess": true,
    "Message": "Initiated Successfully!",
    "ValidationErrors": null,
    "Data": {
        "SessionId": "a76202c2-60ae-4b89-bce1-9b81fae35d03",
        "CountryCode": "KWT",
        "CustomerTokens": []
    }
}
"""
private const val EXECUTE_PAYMENT_RESPONSE = """
{
    "IsSuccess": true,
    "Message": "Invoice Created Successfully!",
    "ValidationErrors": null,
    "Data": {
        "InvoiceId": 4538212,
        "IsDirectPayment": false,
        "PaymentURL": "https://demo.MyFatoorah.com/En/KWT/PayInvoice/MpgsAuthentication?paymentId=07074538212227573",
        "CustomerReference": "123",
        "UserDefinedField": null,
        "RecurringId": ""
    }
}
"""
private const val EXECUTE_SUBSCRIPTION_PAYMENT_RESPONSE = """
{
    "IsSuccess": true,
    "Message": "Invoice Created Successfully!",
    "ValidationErrors": null,
    "Data": {
        "InvoiceId": 4538212,
        "IsDirectPayment": false,
        "PaymentURL": "https://demo.MyFatoorah.com/En/KWT/PayInvoice/MpgsAuthentication?paymentId=07074538212227573",
        "CustomerReference": "123",
        "UserDefinedField": null,
        "RecurringId": "456"
    }
}
"""
