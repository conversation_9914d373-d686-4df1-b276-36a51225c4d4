#-----------------------
# Basic
#-----------------------
application:
  name: fortraders-api-production
  port: 8080

  consuming: true

  resources:
    requests:
      memory: 8192Mi
      cpu: 0.5
    limits:
      memory: 8192Mi
      cpu: 2

  readinessProbe:
    httpGet:
      port: 8282
      path: /actuator/health
    initialDelaySeconds: 120
    timeoutSeconds: 5
    periodSeconds: 15
    failureThreshold: 5

  env:
    - name: SPRING_PROFILES_ACTIVE
      value: production
    - name: BREACH_MONITORING_ENABLED
      value: "true"
    - name: INACTIVITY_BREACH_MONITORING_ENABLED
      value: "true"
    - name: PLATFORM_MODIFYING_ENABLED
      value: "true"
    - name: OTEL_EXPORTER_OTLP_PROTOCOL
      value: http/protobuf
    - name: OTEL_EXPORTER_OTLP_HEADERS
      valueFrom:
        secretKeyRef:
          key: otel.headers
          name: otel-configuration
    - name: CARD_PAYMENT_PROVIDER
      valueFrom:
        secretKeyRef:
          key: fortraders.transaction.card-payment-provider
          name: env-variables
    - name: KYC_PROVIDER
      valueFrom:
        secretKeyRef:
          key: fortraders.kyc.provider
          name: env-variables
    - name: DB_URL
      valueFrom:
        secretKeyRef:
          key: spring.datasource.url
          name: env-variables
    - name: DB_USERNAME
      valueFrom:
        secretKeyRef:
          key: spring.datasource.username
          name: env-variables
    - name: DB_PASSWORD
      valueFrom:
        secretKeyRef:
          key: spring.datasource.password
          name: env-variables
    - name: REDIS_URL
      valueFrom:
        secretKeyRef:
          key: cleevio.distributed-locks.redis.address
          name: env-variables
    - name: SENTRY_DSN
      valueFrom:
        secretKeyRef:
          key: sentry.dsn
          name: env-variables
    - name: FIREBASE_CREDENTIALS
      valueFrom:
        secretKeyRef:
          key: firebase.credentials
          name: env-variables
    - name: FIREBASE_WEBHOOK_SECRET_KEY
      valueFrom:
        secretKeyRef:
          key: fortraders.security.webhook.secret-key.firebase
          name: env-variables
    - name: TRUST_PAY_SECRET_KEY
      valueFrom:
        secretKeyRef:
          key: integration.trust-pay.secret-key
          name: env-variables
    - name: TRUST_PAY_ACCOUNT_ID
      valueFrom:
        secretKeyRef:
          key: integration.trust-pay.account-id
          name: env-variables
    - name: STRIPE_API_KEY
      valueFrom:
        secretKeyRef:
          key: integration.stripe.api-key
          name: env-variables
    - name: STRIPE_WEBHOOK_SECRET_KEY
      valueFrom:
        secretKeyRef:
          key: fortraders.security.webhook.secret-key.stripe
          name: env-variables
    - name: MY_FATOORAH_WEBHOOK_SECRET_KEY
      valueFrom:
        secretKeyRef:
          key: fortraders.security.webhook.secret-key.my-fatoorah
          name: env-variables
    - name: PAYTIKO_WEBHOOK_SECRET_KEY
      valueFrom:
        secretKeyRef:
          key: fortraders.security.webhook.secret-key.paytiko
          name: env-variables
    - name: MY_FATOORAH_API_KEY
      valueFrom:
        secretKeyRef:
          key: integration.my-fatoorah.api-key
          name: env-variables
    - name: PAYTIKO_API_KEY
      valueFrom:
        secretKeyRef:
          key: integration.paytiko.api-key
          name: env-variables
    - name: MAIL_USERNAME
      valueFrom:
        secretKeyRef:
          key: spring.mail.username
          name: env-variables
    - name: MAIL_PASSWORD
      valueFrom:
        secretKeyRef:
          key: spring.mail.password
          name: env-variables
    - name: MT5_USERNAME
      valueFrom:
        secretKeyRef:
          key: integration.mt5.username
          name: env-variables
    - name: MT5_PASSWORD
      valueFrom:
        secretKeyRef:
          key: integration.mt5.password
          name: env-variables
    - name: TRADE_LOCKER_API_KEY
      valueFrom:
        secretKeyRef:
          key: integration.trade-locker.api-key
          name: env-variables
    - name: DX_TRADE_DOMAIN
      valueFrom:
        secretKeyRef:
          key: integration.dx-trade.domain
          name: env-variables
    - name: DX_TRADE_USER
      valueFrom:
        secretKeyRef:
          key: integration.dx-trade.user
          name: env-variables
    - name: DX_TRADE_PASSWORD
      valueFrom:
        secretKeyRef:
          key: integration.dx-trade.password
          name: env-variables
    - name: DX_FEED_API_KEY
      valueFrom:
        secretKeyRef:
          key: integration.dx-feed.api-key
          name: env-variables
    - name: C_TRADER_ACCESS_TOKEN
      valueFrom:
        secretKeyRef:
          key: integration.c-trader.manager.access-token
          name: env-variables
    - name: C_TRADER_MANAGER_LOGIN
      valueFrom:
        secretKeyRef:
          key: integration.c-trader.manager.login
          name: env-variables
    - name: C_TRADER_MANAGER_PASSWORD
      valueFrom:
        secretKeyRef:
          key: integration.c-trader.manager.password
          name: env-variables
    - name: CALENDLY_API_TOKEN
      valueFrom:
        secretKeyRef:
          key: integration.calendly.api-token
          name: env-variables
    - name: SLACK_BOT_TOKEN
      valueFrom:
        secretKeyRef:
          key: integration.slack.bot-token
          name: env-variables
    - name: SUMSUB_API_KEY
      valueFrom:
        secretKeyRef:
          key: integration.sumsub.api-key
          name: env-variables
    - name: SUMSUB_SECRET_KEY
      valueFrom:
        secretKeyRef:
          key: integration.sumsub.secret-key
          name: env-variables
    - name: SUMSUB_WEBHOOK_SECRET_KEY
      valueFrom:
        secretKeyRef:
          key: fortraders.security.webhook.secret-key.sumsub
          name: env-variables
    - name: CONFIRMO_API_KEY
      valueFrom:
        secretKeyRef:
          key: integration.confirmo.api-key
          name: env-variables
    - name: CONFIRMO_WEBHOOK_SECRET_KEY
      valueFrom:
        secretKeyRef:
          key: fortraders.security.webhook.secret-key.confirmo
          name: env-variables
    - name: DX_FEED_WEBHOOK_SECRET_KEY
      valueFrom:
        secretKeyRef:
          key: fortraders.security.webhook.secret-key.dx-feed
          name: env-variables
    - name: DOCUSEAL_WEBHOOK_SECRET_KEY
      valueFrom:
        secretKeyRef:
          key: fortraders.security.webhook.secret-key.docuseal
          name: env-variables
    - name: ECOMAIL_API_KEY
      valueFrom:
        secretKeyRef:
          key: integration.ecomail.api-key
          name: env-variables
    - name: GOOGLE_CALENDAR_SERVICE_ACCOUNT_KEY
      valueFrom:
        secretKeyRef:
          key: google.calendar.service.account.key
          name: env-variables
    - name: WORDPRESS_USERNAME
      valueFrom:
        secretKeyRef:
          key: integration.wordpress.username
          name: env-variables
    - name: WORDPRESS_PASSWORD
      valueFrom:
        secretKeyRef:
          key: integration.wordpress.password
          name: env-variables
    - name: DX_FEED_API_KEY
      valueFrom:
        secretKeyRef:
          key: integration.dx-feed.api-key
          name: env-variables
    - name: INTERCOM_HMAC_SECRET
      valueFrom:
        secretKeyRef:
          key: integration.intercom.hmac-secret
          name: env-variables
    - name: DOCUSEAL_API_KEY
      valueFrom:
        secretKeyRef:
          key: integration.docuseal.api-key
          name: env-variables
    - name: PAYPAL_CLIENT_ID
      valueFrom:
        secretKeyRef:
          key: integration.paypal.client-id
          name: env-variables
    - name: PAYPAL_SECRET_KEY
      valueFrom:
        secretKeyRef:
          key: integration.paypal.secret-key
          name: env-variables
    - name: NUVEI_MERCHANT_ID
      valueFrom:
        secretKeyRef:
          key: integration.nuvei.merchant-id
          name: env-variables
    - name: NUVEI_MERCHANT_SITE_ID
      valueFrom:
        secretKeyRef:
          key: integration.nuvei.merchant-site-id
          name: env-variables
    - name: NUVEI_MERCHANT_SECRET_KEY
      valueFrom:
        secretKeyRef:
          key: integration.nuvei.merchant-secret-key
          name: env-variables
    - name: OPENAI_API_KEY
      valueFrom:
        secretKeyRef:
          key: integration.openai.api-key
          name: env-variables
    - name: TAP_SECRET_KEY
      valueFrom:
        secretKeyRef:
          key: integration.tap.secret-key
          name: env-variables
    - name: TOP_USER_RANKING_SECRET_KEY
      valueFrom:
        secretKeyRef:
          key: fortraders.notification.top-user-ranking.secret-key
          name: env-variables
    - name: VERIFF_API_KEY
      valueFrom:
        secretKeyRef:
          key: integration.veriff.api-key
          name: env-variables
    - name: VERIFF_WEBHOOK_SECRET_KEY
      valueFrom:
        secretKeyRef:
          key: fortraders.security.webhook.secret-key.veriff
          name: env-variables

  image:
    repository: gitlab.cleevio.cz:4567/backend/fortraders-api/production
    tag: latest

  replicaCount: 2

#-----------------------
# Ingress
#-----------------------
ingress:
  certificateIssuer: letsencrypt-http

  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"

  hosts:
    - host: api.fortraders.com
      paths: [ "/" ]

  tls:
    - hosts:
        - api.fortraders.com

#-----------------------
# Service
#-----------------------
service:
  port: 8080

#-----------------------
# Persistent Volume
#-----------------------
persistentVolume:
  enabled: false                     # Enables / Disables mounting

#-------------------
# Secrets Creation
#-------------------
secrets:
  loki:
    enabled: true
    custom: true
  otel:
    enabled: true
    custom: true
